# API Reference

This document provides detailed API reference for all services in the Longvan Storefront JavaScript Client SDK.

## Table of Contents

- [SDK Class](#sdk-class)
- [Authentication Service](#authentication-service)
- [Product Service](#product-service)
- [Order Service](#order-service)
- [User Service](#user-service)
- [Payment Service](#payment-service)
- [CRM Service](#crm-service)
- [Warehouse Service](#warehouse-service)
- [Computing Service](#computing-service)
- [Campaign Service](#campaign-service)
- [Image Service](#image-service)
- [Upload Service](#upload-service)
- [Utility Services](#utility-services)

## SDK Class

### Constructor

```typescript
new SDK(orgId: string, storeId: string, environment: 'dev' | 'live')
```

**Parameters:**
- `orgId` - Organization identifier
- `storeId` - Store identifier  
- `environment` - Environment ('dev' for development, 'live' for production)

### Methods

#### setToken(token: string)
Sets the authentication token for all services.

#### setStoreId(storeId: string)
Updates the store ID for all services.

#### setOrgId(orgId: string)
Updates the organization ID for all services.

## Authentication Service

### login(loginRequest: LoginRequest): Promise<LoginResponse>

Authenticates a user with username and password.

**Parameters:**
```typescript
interface LoginRequest {
  username: string;
  password: string;
}
```

**Returns:**
```typescript
interface LoginResponse {
  partyId: string;
  orgId: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  identityNumber: string;
  gender: string;
  birthDate: string;
  avatarUrl: string;
  accessToken: string;
  username: string;
  orgPermissionsMap: Record<string, any>;
  orgPositionsMap: Record<string, any>;
  orgRolesMap: Record<string, any>;
}
```

### register(registerRequest: RegisterRequest): Promise<void>

Registers a new user account.

**Parameters:**
```typescript
interface RegisterRequest {
  username: string;
  fullName: string;
  password: string;
  userIP: string;
}
```

### getUserDetail(accessToken: string): Promise<any>

Retrieves user details using an access token.

### sendSmsVerifyCode(username: string): Promise<SendSmsVerifyCodeResponse>

Sends SMS verification code for password reset.

### verifyCode(request: SmsVerifyCodeRequest): Promise<any>

Verifies SMS code.

**Parameters:**
```typescript
interface SmsVerifyCodeRequest {
  username: string;
  code: string;
}
```

### resetPassword(request: ResetPasswordRequest): Promise<ResetPasswordResponse>

Resets user password.

**Parameters:**
```typescript
interface ResetPasswordRequest {
  username: string;
  newPassword: string;
  accessToken: string;
}
```

### Social Authentication Methods

#### loginGoogle(redirectUrl: string): Promise<any>
Initiates Google OAuth login.

#### loginFacebook(redirectUrl: string): Promise<any>
Initiates Facebook OAuth login.

#### loginZalo(redirectUrl: string): Promise<any>
Initiates Zalo OAuth login.

## Product Service

### getProductById(productId: string): Promise<Product>

Retrieves a product by its ID.

### getProductBySlug(slug: string): Promise<Product>

Retrieves a product by its slug.

### getSimpleProducts(options: ProductFilterOptions): Promise<Product[]>

Retrieves products with filtering and pagination.

**Parameters:**
```typescript
interface ProductFilterOptions {
  partnerId?: string;
  storeChannel?: string;
  category?: string;
  product?: string;
  sku?: string;
  tag?: string;
  priceFrom?: number;
  priceTo?: number;
  status?: string;
  productType?: string;
  subType?: string;
  brandId?: string;
  keyword?: string;
  display?: boolean;
  onlyPromotion?: boolean;
  currentPage?: number;
  maxResult?: number;
}
```

### getCategory(typeBuild: string, level: number): Promise<Category[]>

Retrieves product categories.

### Product Management Methods

#### updateProductTitle(productId: string, title: string): Promise<void>
Updates product title.

#### updatePrice(productId: string, price: number): Promise<void>
Updates product price.

#### updatePricePromotion(productId: string, promotionPrice: number): Promise<void>
Updates product promotion price.

#### updateCategory(productId: string, categoryId: string): Promise<void>
Updates product category.

#### updateShortDescription(productId: string, description: string): Promise<void>
Updates product short description.

#### updateUnit(productId: string, unit: string): Promise<void>
Updates product unit.

#### clearAllCaches(): Promise<void>
Clears all product caches.

## Order Service

### createOrder(orderData: any, platform: string, createDraft: boolean, createdBy: string): Promise<any>

Creates a new order.

### getOrderDetail(orderId: string): Promise<any>

Retrieves order details.

### addOrderLineItems(orderId: string, lineItems: LineItem[]): Promise<any>

Adds line items to an existing order.

**Parameters:**
```typescript
interface LineItem {
  quantity: number;
  parent_id?: string;
  product_id: string;
  input_price?: number;
  discount_amount?: number;
}
```

### updateOrderStatus(orderId: string, status: string, updatedBy: string): Promise<any>

Updates order status.

### cancelOrder(orderId: string, cancelData: CancelOrder): Promise<any>

Cancels an order.

**Parameters:**
```typescript
interface CancelOrder {
  reason: "CUSTOMER";
  updatedBy: string;
  note: string;
  orderType: "SALES";
}
```

### Order Query Methods

#### getOrders(query: OrderQuery): Promise<any>
Retrieves orders with filtering.

#### getOrdersByCustomerId(customerId: string, currentPage: number, maxResult: number): Promise<any>
Retrieves orders for a specific customer.

#### getOrdersByStatus(status: string[], currentPage: number, maxResult: number): Promise<any>
Retrieves orders by status.

### Shipping Methods

#### getListShippingService(shippingCarrierId: string): Promise<any>
Gets available shipping services.

#### calculateShippingFee(data: any): Promise<any>
Calculates shipping fees.

### Address Management

#### createInfoReceiver(ownerId: string, address: ShippingAddress): Promise<any>
Creates receiver information.

#### getInfoReceiver(ownerId: string): Promise<any>
Gets receiver information.

**Parameters:**
```typescript
interface ShippingAddress {
  name: string;
  phone: string;
  address: string;
  province_code: string;
  district_code: string;
  ward_code: string;
  address_default?: boolean;
}
```

#### updateCustomerInfoFront(orderId: string, customerInfo: CustomerInfo): Promise<any>
Updates customer information for an order in front-end context.

**Parameters:**
```typescript
interface CustomerInfo {
  id: string;
  email: string;
  name: string;
  phone: string;
}
```

## User Service

### searchCustomer(searchParams: customerQuery): Promise<searchCustomersResponse>

Searches for customers.

**Parameters:**
```typescript
interface customerQuery {
  keyword?: string;
  type?: string;
  startCreatedDate?: string;
  endCreatedDate?: string;
  memberLevel?: string;
  currentPage?: number;
  pageSize?: number;
}
```

### createCustomerV2(request: createCustomerRequest, createdBy: string): Promise<any>

Creates a new customer.

**Parameters:**
```typescript
interface createCustomerRequest {
  name: string;
  phone?: string;
  email?: string;
  birthDate?: string;
}
```

### getCustomerById(customerId: string): Promise<any>

Retrieves customer by ID.

### updateCustomerV2(customerId: string, request: updateCustomerRequest, updatedBy: string): Promise<any>

Updates customer information.

### Company Management

#### createCompany(request: createCompanyRequest, createdBy: string): Promise<CreateCompany>
Creates a new company.

#### searchCompany(keyword: string, currentPage: number, pageSize: number): Promise<any>
Searches for companies.

### Employee Management

#### searchEmployees(query: employeesQuery): Promise<any>
Searches for employees.

#### getPositionsByEmployeesId(employeeId: string): Promise<any>
Gets employee positions.

#### getStoreChannelIdsByEmployeesId(employeeId: string): Promise<any>
Gets store channels for employee.

### Location Services

#### getProvinces(): Promise<any>
Gets list of provinces.

#### getDistricts(provinceCode: string): Promise<any>
Gets districts for a province.

#### getWards(districtCode: string): Promise<any>
Gets wards for a district.

## Payment Service

### getPaymentMethodOfStoreChannel(): Promise<any>

Gets available payment methods for the store.

### genQrPayment(orderId: string, paymentMethodId: string): Promise<any>

Generates QR code for payment.

### createPaymentOrder(paymentData: any): Promise<any>

Creates a payment order.

### getInvoiceDetail(invoiceId: string): Promise<any>

Gets invoice details.

### getInvoicesOfOrder(orderId: string): Promise<any>

Gets all invoices for an order.

### VAT Invoice Methods

#### requestPublishVatInvoice(request: VatInvoiceRequestDTO): Promise<any>
Requests VAT invoice publication.

#### requestUnpublishVatInvoice(invoiceId: string): Promise<any>
Requests VAT invoice unpublication.

#### viewPublishedInvoice(invoiceId: string): Promise<any>
Views published invoice.

## CRM Service

### Work Effort Management

#### createWorkEffort(createdBy: string, name: string, description: string, workEffortTypeId: string, source: string, attributes: object, addAttachmentRequest: any, parentId?: string): Promise<any>
Creates a new work effort.

#### getWorkEffortById(workEffortId: string): Promise<any>
Gets work effort by ID.

#### updateWorkEffortStatus(workEffortId: string, status: string, updatedBy: string): Promise<any>
Updates work effort status.

### Opportunity Management

#### addOpportunity(performerId: string, request?: any): Promise<any>
Adds a new opportunity.

#### getListOpportunity(performerId: string, currentPage: number, pageSize: number): Promise<any>
Gets list of opportunities.

### Ticket Management

#### addTicket(request: any): Promise<any>
Creates a new ticket.

#### getListTicket(workEffortTypeId: string, currentPage: number, pageSize: number, status?: string): Promise<any>
Gets list of tickets.

#### getTicketById(ticketId: string): Promise<any>
Gets ticket by ID.

### Comment and Attachment Management

#### addComment(workEffortId: string, comment: string, createdBy: string): Promise<any>
Adds comment to work effort.

#### getListComment(workEffortId: string, currentPage: number, pageSize: number): Promise<any>
Gets comments for work effort.

#### addAttachmentForWorkEffort(workEffortId: string, attachmentRequest: any): Promise<any>
Adds attachment to work effort.

## Warehouse Service

### getInventory(sku: string, warehouseId: string): Promise<any>

Gets inventory for a product SKU in a warehouse.

## Warehouse Service V2

### getInventory(warehouseId: string, products: any[]): Promise<any>

Gets inventory for multiple products in a warehouse (batch operation).

## Computing Service

### computingDetail(computingId: string): Promise<any>

Gets computing service details.

## Campaign Service

Campaign service methods for managing marketing campaigns and promotions.

## Image Service

Image service methods for managing product images and media.

## Upload Service

File upload service methods for handling document and media uploads.

## Utility Services

### Portal Service
Administrative portal operations.

### Comhub Service  
Communication hub for messaging and notifications.

### Omnigateway Service
Omnichannel gateway for multi-channel integration.

### Authorization Service
JWT token management operations.

### ZCA Service
Zalo platform integration services.

---

For more examples and usage patterns, see the main [README.md](README.md) file.
