// lib/order/index.ts
import {
  LineItem,
  DiscountCampaign,
  CancelOrder,
  MemberDiscount,
  CampaignPromotion,
  OrderQuery,
  ShippingAddress,
  CustomerInfo,
} from "../../types/order";
import { Service } from "../serviceSDK";
import { validatePhoneNumber } from "../../utils/validatePhoneNumber";

/**
 * Represents a service for managing orders.
 */
export class OrderService extends Service {
  /**
   * Constructs a new OrderService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  setToken(token: string) {
    this.token = token;
  }

  /**
   * Creates a new order.
   * @param orderData - The data for the order.
   * @param platform - The platform for the order.
   * @param createDraft - Indicates whether to create a draft order.
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async createOrder(
    orderData: any,
    platform: string,
    createDraft: boolean,
    created_by: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${platform}?create_draft=${createDraft}&created_by=${created_by}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        orderData
      );
      return response;
    } catch (error) {
      console.log(`Error in createOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Creates a temporary order.
   *
   * @param orderData - The data for the order.
   * @param platform - The platform for the order.
   * @returns A promise that resolves to the response from the server.
   * @throws If an error occurs during the API call.
   */
  async createOrderTemp(orderData: any, platform: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${platform}/temp`; // Replace with your actual endpoint
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        orderData
      );
      return response;
    } catch (error) {
      console.log(`Error in createOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Completes multiple orders.
   * @param orderIds - The IDs of the orders to complete.
   * @returns A promise that resolves when the orders are completed.
   * @throws If an error occurs while completing the orders.
   */
  async completeOrder(orderIds: string[]) {
    const endpoint = `/orders/${this.orgId}/${
      this.storeId
    }/complete?order_ids=${orderIds.join(",")}`; // Replace with your actual endpoint
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in completeOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Adds a voucher to an order.
   * @param orderId - The ID of the order.
   * @param voucherCode - The voucher code.
   * @returns A promise that resolves when the voucher is added.
   * @throws If an error occurs while adding the voucher.
   */
  async addVoucher(orderId: string, voucherCode: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/voucher/${voucherCode}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in addVoucher: ${error}`);
      throw error;
    }
  }

  /**
   * Removes a voucher from an order.
   * @param orderId - The ID of the order.
   * @param voucherCode - The voucher code.
   * @returns A promise that resolves when the voucher is removed.
   * @throws If an error occurs while removing the voucher.
   */
  async removeVoucher(orderId: string, voucherCode: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/voucher/${voucherCode}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in removeVoucher: ${error}`);
      throw error;
    }
  }

  /**
   * Updates the VAT (Value Added Tax) for an order.
   * @param orderId - The ID of the order.
   * @param vatFee - The VAT fee.
   * @param vatType - The VAT type. (e.g., VALUE_GOODS, PRODUCT, etc.    )
   * @returns A promise that resolves when the VAT is updated.
   * @throws If an error occurs while updating the VAT.
   */
  async updateVAT(orderId: string, vatFee: number, vatType: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/vat?vat_fee=${vatFee}&vat_type=${vatType}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateVAT: ${error}`);
      throw error;
    }
  }
  /**
   * Updates the customer and shipping address for an order.
   * @param orderId - The ID of the order.
   * @param customerId - The data for the customer.
   * @param shippingAddress - The shipping address.
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateCustomerAndShippingAddress(
    orderId: string,
    customerId: string,
    shippingAddress: string
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/customer`;
    const method: "PUT" = "PUT";
    const requestData = {
      customerId: customerId,
      shippingAddressId: shippingAddress,
    };
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateCustomerAndShippingAddress: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves the order line items for a specific order.
   * @param partnerId - The partner ID.
   * @returns A promise that resolves with the order line items.
   * @throws If an error occurs while retrieving the order line items.
   */
  /**
   * Adds order line items to an order.
   * @param orderId - The ID of the order.
   * @param lineItems - The line items to add.
   * @returns A promise that resolves when the line items are added.
   * @throws If an error occurs while adding the line items.
   */
  async addOrderLineItems(orderId: string, lineItems: LineItem[]) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/orderLineItem`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        lineItems
      );
      return response;
    } catch (error) {
      console.log(`Error in addOrderLineItems: ${error}`);
      throw error;
    }
  }
  /**
   * Update Quantity in an order
   * @param orderId - The id of the order .
   * @param orderItemId - The id of order item.
   * @param quantity - Quantity of profuct in order.
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async updateQuantityProductInOrder(
    orderId: string,
    orderItemId: string,
    quantity: number
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}/quantity?quantity_new=${quantity}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in update quantity product in order: ${error}`);
      throw error;
    }
  }
  /**
   *Updates sale Employee
   * @param orderId - The ID of the order.
   * @param saleId - The Id of saler.
   * @param updatedBy - Who update.
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */

  async updateSaleEmployee(orderId: string, saleId: string, updatedBy: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/saleEmployee/${saleId}?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateSaleEmployee: ${error}`);
      throw error;
    }
  }

  /**
   *Update type of order
   * @param orderId - The ID of the order.
   * @param orderType - The type of order.
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateOrderType(orderId: string, orderType: string, updatedBy: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderType}?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateOrderType: ${error}`);
      throw error;
    }
  }
  /**
   * Update new price in order
   * @param orderId - The ID of the order.
   * @param orderItemId - The id of order.
   * @param priceNew - the new price after update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updatePriceInOrder(
    orderId: string,
    orderItemId: string,
    priceNew: number,
    reason: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}/price?price_new=${priceNew}`;
    const method: "PUT" = "PUT";
    const dataReq = {
      reason,
    };
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        dataReq
      );
      return response;
    } catch (error) {
      console.log(`Error in updatePriceInOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Update discount price in order
   * @param orderId - The ID of the order.
   * @param orderItemId - The id of order.
   * @param requestData - The request data
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateDiscountPriceInOrder(
    orderId: string,
    orderItemId: string,
    requestData: DiscountCampaign
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}/discount`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateDiscountPriceInOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Update cancel order
   * @param orderId - The ID of the order.
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateCancelOrder(orderId: string, requestData: CancelOrder) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/cancel`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateCancelOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Update status of return order
   * @param orderId - The ID of the order.
   * @param statusNew - the new status of order
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateStatusReturnOrder(
    orderId: string,
    statusNew: string,
    updatedBy: string
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${statusNew}?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateStatusReturnOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Update Shipping service
   * @param orderId - The ID of the order.
   * @param shippingServiceId - the id of shipping service
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateShippingService(
    orderId: string,
    shippingServiceId: string,
    updatedBy: string
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${shippingServiceId}/shippingService?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateShippngService: ${error}`);
      throw error;
    }
  }
  /**
   * Update shipping order
   * @param orderId - The ID of the order.
   * @param shippingId - the id of shipping
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateShippingOrder(orderId: string, shippingId: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${shippingId}/orderShipping`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateShippingOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Update shipping fee
   * @param orderId - The ID of the order.
   * @param shippingFee - the fee of shipping
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateShippingFee(orderId: string, shipingFee: Number) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${shipingFee}/shippingFee`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateShippingFee: ${error}`);
      throw error;
    }
  }
  /**
   * Update Financial Status after payment success
   * @param orderId - The ID of the order.
   * @param financialStatus - the status of financial
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateFinancialStatus(
    orderId: string,
    financialStatus: string,
    updatedBy: string
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${financialStatus}/financialStatus?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateFinancialStatus: ${error}`);
      throw error;
    }
  }
  /**
   * Update warehouse of order
   * @param orderId - The ID of the order.
   * @param warehouseId - the status of warehouse
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateWareHouseOrder(orderId: String, warehouseId: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/warehouse/${warehouseId}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateWareHouseOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Update voucher
   * @param orderId - The ID of the order.
   * @param voucherCode - the code of voucher
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateVoucher(orderId: string, voucherCode: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/voucher/${voucherCode}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateVoucher: ${error}`);
      throw error;
    }
  }

  /**
   * Update Vat
   * @param orderId - The ID of the order.
   * @param vatFee - The fee of VAT
   * @param vatType - The type of vat
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateVat(orderId: string, vatFee: number, vatType: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/vat?vat_fee=${vatFee}&vat_type=${vatType}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateVat: ${error}`);
      throw error;
    }
  }
  /**
   * Update Status of l order
   * @param orderId - The ID of the order.
   * @param status - The status of sell order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateStatusSellOrder(orderId: string, status: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/status/${status}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateStatusSellOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Update date remain in total price
   * @param orderId - The ID of the order.
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateRemainToTalPrice(orderId: string, updatedBy: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/remainTotalPrice?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in update updateRemainToTalPrice: ${error}`);
      throw error;
    }
  }
  /**
   * Update date create order
   * @param orderId - The ID of the order.
   * @param orderDate - The date of order
   * @param updatedBy - Id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateDateCreateOrder(
    orderId: string,
    orderDate: number,
    updatedBy: string
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/order-date/${orderDate}?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateDateCreateOrder:${error} `);
      throw error;
    }
  }

  /**
   * Update membership discount
   * @param orderId - The ID of the order.
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateMemberDiscount(orderId: string, requestData: MemberDiscount) {
    const endpoint = `/orders/${this.orgId}/${orderId}/memberDiscount`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateMemberDiscount: ${error}`);
      throw error;
    }
  }
  /**
   * Update status editable order
   * @param orderId - The ID of the order.
   * @param editable - The editable of
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateEditableOrder(orderId: string, editable: boolean) {
    const endpoint = `/orders/${this.orgId}/${orderId}/editable/${editable}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateEditableOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Update discount
   * @param orderId - The ID of the order.
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */

  async updateDiscount(orderId: string, updated_by: string, requestData: any) {
    const endpoint = `/orders/${this.orgId}/${orderId}/discount?updated_by=${updated_by}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateDiscount: ${error}`);
      throw error;
    }
  }

  /**
   * Update info campaign promotion
   * @param orderId - The ID of the order.
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateInfoCampaignPromotion(
    orderId: string,
    requestData: CampaignPromotion
  ) {
    const endpoint = `/orders/${this.orgId}/${orderId}/campaign-promotion`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        requestData
      );
      return response;
    } catch (error) {
      console.log(`Error in updateInfoCampaignPromotion: ${error}`);
      throw error;
    }
  }

  /**
   * Get list shipping service
   * @param shippingCarrierId - The ID of the campaign
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getShippingService(shippingCarrierId: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${shippingCarrierId}/shippingService`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListShippingService: ${error}`);
      throw error;
    }
  }

  /**
   * Get info sell order
   * @param orderId - The ID of the order.
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getInfoSellOrder(orderId: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/sell_order`;
    const method: "GET" = "GET";
    try {
      const res = await this.restApiCallWithToken(endpoint, method);
      return res;
    } catch (error) {
      console.log(`Error in getInfoSellOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Get info return order
   * @param orderId - The ID of the order.
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getInfoReturnOrder(orderId: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/return_order`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getReturnOrder:${error}`);
      throw error;
    }
  }

  /**
   * Get list type order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListTypeOrder() {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/type`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateListTypeOrder`);
      throw error;
    }
  }
  /**
   * GEt list shipping carrier
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListShippingCarrier() {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/shippingCarrier`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateListShippingCarrier:${error}`);
      throw error;
    }
  }
  /**
   * Get list sell order
   * @param orderId
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListSellOrder(requestData: OrderQuery) {
    // Convert requestData to a format suitable for URLSearchParams
    const params = new URLSearchParams(
      Object.entries(requestData).reduce((acc, [key, value]) => {
        acc[key] = Array.isArray(value) ? value.join(",") : value.toString();
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    const endpoint = `/orders/${this.orgId}/${this.storeId}/sell_order?${params}`;
    const method: "GET" = "GET";

    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.error(`Error in getListSellOrder: ${error}`);
      throw error;
    }
  }
  // get list all store
  async getListSellOrderAll(requestData: OrderQuery) {
    // Convert requestData to a format suitable for URLSearchParams
    const params = new URLSearchParams(
      Object.entries(requestData).reduce((acc, [key, value]) => {
        acc[key] = Array.isArray(value) ? value.join(",") : value.toString();
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    const endpoint = `/orders/${this.orgId}/ALL/sell_order?${params}`;
    const method: "GET" = "GET";

    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.error(`Error in getListSellOrder: ${error}`);
      throw error;
    }
  }

  async getDiaries(requestData: OrderQuery) {
    // Convert requestData to a format suitable for URLSearchParams
    const params = new URLSearchParams(
      Object.entries(requestData).reduce((acc, [key, value]) => {
        acc[key] = Array.isArray(value) ? value.join(",") : value.toString();
        return acc;
      }, {} as Record<string, string>)
    ).toString();

    const endpoint = `/orders/${this.orgId}/${this.storeId}/diaries?${params}`;
    const method: "GET" = "GET";

    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.error(`Error in getListSellOrder: ${error}`);
      throw error;
    }
  }

  /**
   * Get list sale order status
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListSaleOrderStatus() {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/saleOrderStatus`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListSaleOrderStatus:${error}`);
      throw error;
    }
  }
  /**
   * Get list return order
   * @param orderId - The id of the order
   * @param requestData
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListReturnOrder(requestData: OrderQuery) {
    const params = new URLSearchParams(
      Object.entries(requestData).reduce((acc, [key, value]) => {
        acc[key] = Array.isArray(value) ? value.join(",") : value.toString();
        return acc;
      }, {} as Record<string, string>)
    ).toString();
    const endpoint = `/orders/${this.orgId}/${this.storeId}/return_order?${params}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListReturnOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Get list return order status
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListReturnOrderStatus() {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/returnOrderStatus`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListReturnOrderStatus: ${error}`);
      throw error;
    }
  }
  /**
   * remove draft order
   * @param orderId - The id of order
   * @param updatedBy - Thi id of person update
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async removeDraftOrder(orderId: string, updatedBy: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/draft?updated_by=${updatedBy}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in removeDraftOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Cancel product in order
   * @param orderId - The id of order
   * @param orderItemId - Thi id of item order
   * @param reason - The reason of cancel product in order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async cancelProductInOrder(
    orderId: string,
    orderItemId: string,
    reason: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}?reason=${reason}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in removeProductInOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Remove Product in order
   * @param orderId - The id of order
   * @param orderItemId - Thi id of item order
   * @param reason - The reason of cancel product in order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async removeProductInOrder(
    orderId: string,
    orderItemId: string,
    reason: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}/delete?reason=${reason}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in removeProductInOrder: ${error}`);
      throw error;
    }
  }
  /**
   * Print order pdf
   * @param orderId - The id of order
   * @param paymentMethod - Thi id of item order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async printOrderPdf(orderId: string, paymentMethod: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/print.pdf?payment_method=${paymentMethod}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      console.log(response);

      return response;
    } catch (error) {
      console.log(`Error in printOrderPdf: ${error}`);
      throw error;
    }
  }
  /**
   * Print order HTML
   * @param orderId - The id of order
   * @param paymentMethod - Thi id of item order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async printOrderHtml(
    orderId: string,
    paymentMethod?: string,
    paymentUrl?: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/print.html?payment_method=${paymentMethod}&payment_url=${paymentUrl}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in printOrderHtml: ${error}`);
      throw error;
    }
  }
  /**
   * Create an orderReturn
   * @param orderData - The id of order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async createOrderReturn(orderData: any, created_by: any) {
    const endpoint = `/orders/${this.orgId}/order_return?created_by=${created_by}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        orderData
      );
      return response;
    } catch (error) {
      console.log(`Error in createOrderReturn: ${error}`);
      throw error;
    }
  }
  /**
   * Caculate the order
   * @param orderData - The data from the order
   * @returns A promise that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async calculateOrder(orderData: any) {
    const endpoint = `/orders/${this.orgId}/calculate`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        orderData
      );
      return response;
    } catch (error) {
      console.log(`Error in calculateOrder: ${error}`);
      throw error;
    }
  }
  /**
   * get amount product able order
   * @param productIds - The id of product
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getQuantityAbleOrder(productIds: string[]) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/quantity-future-able?product_ids=${productIds}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getQuantityAbleOrder: ${error}`);
      throw error;
    }
  }
  /**
   * updatee note without login
   * @param orderId - The id of the order
   * @param noteId - The id of the note
   * @param note - The content of the note
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async updateNoteWithoutLogin(orderId: string, noteId: string, note: string) {
    const endpoint = `/front/orders/${this.orgId}/${this.storeId}/${orderId}/${noteId}/note`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method, note);
      return response;
    } catch (error) {
      console.log(`Error in updateNoteWithout:${error}`);
      throw error;
    }
  }
  /**
   * add voucher without login
   * @param orderId - The id of the order
   * @param voucherCode - The code of voucher
   * @param updatedBy
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async addVoucherWithoutLogin(
    orderId: string,
    voucherCode: string,
    updatedBy: "SYSTEM"
  ) {
    const endpoint = `/front/orders/${this.orgId}/${orderId}/voucher/${voucherCode}?updated_by=${updatedBy}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in addVoucherWithout: ${error}`);

      throw error;
    }
  }
  /**
   * delete voucher without login
   * @param orderId - The id of the order
   * @param voucherCode - The code of voucher
   * @param updatedBy
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async deleteVoucherLogin(
    orderId: string,
    voucherCode: string,
    updatedBy: "SYSTEM"
  ) {
    const endpoint = `/front/orders/${this.orgId}/${orderId}/voucher/${voucherCode}?updatedBy=${updatedBy}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in deleteVoucher: ${error}`);
      throw error;
    }
  }
  /**
   * get list  note without login
   * @param orderId - The id of the order
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListNoteWithoutLogin(orderId: string) {
    const endpoint = `/front/orders/${this.orgId}/${this.storeId}/${orderId}/note`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListNoteWithoutLogin:${error}`);
      throw error;
    }
  }
  /**
   * create note without login
   * @param orderId - The id of the order
   * @param createdBy
   * @param note
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async createNoteWithoutLogin(
    orderId: string,
    createdBy: string,
    note: string
  ) {
    const endpoint = `/front/orders/${this.orgId}/${this.storeId}/${orderId}/note?created_by=${createdBy}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(endpoint, method, note);
      return response;
    } catch (error) {
      console.log(`Error in createNoteWithoutLogin: ${error}`);
      throw error;
    }
  }
  /**
   * get list order realation
   * @param orderIds
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async getListOrderRelationsWithoutLogin(orderIds: string[]) {
    const endpoint = `/front/orders/${this.orgId}/${
      this.storeId
    }/relations?order_ids=${orderIds.join()}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getListOrderRelationsWithoutLogin: ${error}`);
      throw error;
    }
  }

  async getOrderPromotion(level: string) {
    const endpoint = `/member-promotions/${this.orgId}/level/${level}/order-promotion`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in getOrderPromotion: ${error}`);
      throw error;
    }
  }
  /**
   * get list order realation
   * @param orderId - The id of order
   * @param noteId - The id of note
   * @param deletedBy - The id of person delete
   * @returns A prom that resolves when the customer and shipping address are updated.
   * @throws If an error occurs while updating the customer and shipping address.
   */
  async deleteNoteWithoutLogin(
    orderId: string,
    noteId: string,
    deletedBy: string
  ) {
    const endpoint = `/front/orders/${this.orgId}/${this.storeId}/${orderId}/${noteId}?deleted_by=${deletedBy}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in deleteNoteWithoutLogin: ${error}`);
      throw error;
    }
  }
  async getOrderByIdNoLogin(
    partnerId: string,
    storeId: string,
    orderId: string
  ) {
    const endpoint = `/front/orders/express/${partnerId}/${storeId}?order_id=${orderId}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async updateOrderDescription(orderId: string, description: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/description`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        description,
        {
          "Content-Type": "text/plain",
        }
      );
      return response;
    } catch (error) {
      console.log(`Error in updateOrderDescription: ${error}`);
      throw error;
    }
  }
  async getOrderDetail(orderId: string) {
    return this.restApiCallWithToken(
      `/orders/${this.orgId}/${this.storeId}/${orderId}/sale_order`,
      "GET"
    );
  }
  async createInfoReceiver(ownerId: string, dataRequest: ShippingAddress) {
    const endpoint = `/receiver-infos/${this.orgId}/owner/${ownerId}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        dataRequest
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getInfoReceiver(ownerId: string) {
    const endpoint = `/receiver-infos/${this.orgId}/owner/${ownerId}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async updateInfoReceiver(
    ownerId: string,
    receiverId: string,
    updatedBy: string,
    dataRequest: ShippingAddress
  ) {
    const endpoint = `/receiver-infos/${this.orgId}/owner/${ownerId}/${receiverId}?updated_by=${updatedBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        dataRequest
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async deleteInfoReceiver(
    ownerId: string,
    receiverId: string,
    deletedBy: string
  ) {
    const endpoint = `/receiver-infos/${this.orgId}/owner/${ownerId}/${receiverId}?deleted_by=${deletedBy}`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async enableProductDiary(orderId: string, orderItemId: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${orderItemId}/enable`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  // remove mamber discount
  async removeMemberDiscount(orderId: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/discountMember`;
    const method: "DELETE" = "DELETE";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async getInfoChatApp(attributesName: string) {
    const endpoint = `/store-channels/${this.orgId}/${this.storeId}/attributes/${attributesName}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  validatePhoneNumber(phoneNumer: string) {
    const res = validatePhoneNumber(phoneNumer);
    return res;
  }
  // statistic
  /**
   *report by date month year
   * @param employee_assign
   * @param type_view
   * @param date_from
   * @param date_to
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async reportDateMonthYear(
    employee_assign: string,
    type_view: string,
    date_from: number,
    date_to: number
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${employee_assign}/reportDateMonthYear?type_view=${type_view}&date_from=${date_from}&date_to=${date_to}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * report by sale employee
   * @param employee_assign
   * @param date_from
   * @param date_to
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async reportBySaleEmployee(
    employee_assign: string,
    date_from: number,
    date_to: number
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${employee_assign}/reportBySaleEmployee?date_from=${date_from}&date_to=${date_to}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * report by sale employee
   * @param employee_assign
   * @param date_from
   * @param date_to
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async reportByPaymentMethod(
    employee_assign: string,
    date_from: number,
    date_to: number
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${employee_assign}/reportByPaymentMethod?date_from=${date_from}&date_to=${date_to}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * report by detail
   * @param employee_assign
   * @param type_view
   * @param date_from
   * @param date_to
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async reportByDetail(
    employee_assign: string,
    type_view: string,
    date_from: number,
    date_to: number
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${employee_assign}/reportByDetail?type_view=${type_view}&date_from=${date_from}&date_to=${date_to}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * report by store
   * @param date_from
   * @param date_to
   * @returns A promise that resolves with the created order.
   * @throws If an error occurs while creating the order.
   */
  async reporByStores(date_from: number, date_to: number) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/reportByStores?date_from=${date_from}&date_to=${date_to}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async updateExchangeOrder(
    exchangeOrder: string,
    returnOrder: string,
    sellOrder: string
  ) {
    const endpoint = `/orders/${this.orgId}/${exchangeOrder}/${returnOrder}/${sellOrder}/updateExchangeOrder`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async removeShippingAddress(orderId: string, updateBy: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/removeShippingAddressInfo?updated_by=${updateBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async removeShippingInfo(orderId: string, updateBy: string) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/removeShippingInfo?updated_by=${updateBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async updateVatInorder(
    orderId: string,
    orderItemId: string,
    vatRate: string
  ) {
    const endpoint = `/orders/${this.orgId}/${this.storeId}/${orderId}/${orderItemId}/${vatRate}/vatProduct`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  /**
   * Updates the referral code for an order
   * @param orderId - The ID of the order
   * @param referralCode - The referral code to update
   * @returns A promise that resolves when the referral code is updated
   * @throws If an error occurs while updating the referral code
   */
  async updateReferralCode(orderId: string, referralCode: string) {
    const endpoint = `/orders/${this.orgId}/${orderId}/${referralCode}/updateReferralCode`;
    const method: "PUT" = "PUT";
    console.log("🚀 ~ OrderService ~ updateReferralCode ~ endpoint:", endpoint)
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      console.log(`Error in updateReferralCode: ${error}`);
      throw error;
    }
  }

  /**
   * Updates customer information for an order in front-end context
   * @param orderId - The ID of the order
   * @param customerInfo - The customer information to update
   * @returns A promise that resolves when the customer information is updated
   * @throws If an error occurs while updating the customer information
   */
  async updateCustomerInfoFront(
    orderId: string,
    customerInfo: CustomerInfo
  ) {
    const endpoint = `/front/orders/${this.orgId}/${orderId}/customerInfo`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(
        endpoint,
        method,
        customerInfo
      );
      return response;
    } catch (error) {
      console.log(`Error in updateCustomerInfoFront: ${error}`);
      throw error;
    }
  }
}
