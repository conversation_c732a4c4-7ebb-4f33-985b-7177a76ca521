// src/SDK.ts
import { ProductService } from "../lib/product/index";
import { AuthService } from "../lib/auth/index";
import { OrderService } from "../lib/order/index";
import { ServiceManagementService } from "./service/index";
import { UserService } from "../lib/user/index";
import { environmentEndpoints } from "../../config/config";
import { PaymentService } from "../lib/payment/index";
import { CrmService } from "../lib/crm/index";
import { WarehouseService } from "../lib/warehouse/index";
import { ComputingService } from "../lib/computing/index";
import { CampaignService } from "./campaign";
import { ImageService } from "./image";
import { PaymentServiceV2 } from "./paymentV2";
import { WarehouseServiceV2 } from "./warehouseV2";
import { DeepLinkVietQrService } from "./deepLinkVietQr";
import { ComhubService } from "./comhub";
import { PortalService } from "./portal";
import { UploadService } from "./upload";
import { GetImageService } from "./getImage";
import { AccountingService } from "./accounting";
import { OmnigatewayService } from "./omnigateway";
import { AuthorizationService } from "./token";
import { ZcaService } from "./zca";
import { CashbookService } from "./cashbook";
export interface Endpoints {
  product: string;
  crm: string;
  auth: string;
  order: string;
  user: string;
  payment: string;
  service: string;
  warehouse: string;
  computing: string;
  campaign: string;
  image: string;
  paymentV2: string;
  warehouseV2: string;
  deepLinkVietQr: string;
  comhub: string;
  portal: string;
  upload: string;
  getImage: string;
  accounting: string;
  omnigateway: string;
  authorization: string;
  zca: string;
  cashbook: string;
}

export class SDK {
  public product: ProductService;
  public auth: AuthService;
  public order: OrderService;
  public user: UserService;
  public payment: PaymentService;
  public crm: CrmService;
  public service: ServiceManagementService;
  public warehouse: WarehouseService;
  public computing: ComputingService;
  public campaign: CampaignService;
  public image: ImageService;
  public paymentV2: PaymentServiceV2;
  public warehouseV2: WarehouseServiceV2;
  public deepLinkVietQr: DeepLinkVietQrService;
  public comhub: ComhubService;
  public portal: PortalService;
  public upload: UploadService;
  public getImage: GetImageService;
  public accounting: AccountingService;
  public omnigateway: OmnigatewayService;
  public authorization: AuthorizationService;
  public zca: ZcaService;
  public cashbook: CashbookService;
  public token: string | null = null;

  // Cache for service instances to optimize iteration
  private _serviceInstances: Array<{ name: string; instance: any }> | null =
    null;
  constructor(
    public orgId: string,
    public storeId: string,
    public environment: "dev" | "live"
  ) {
    const endpoints: Endpoints = environmentEndpoints[environment];
    this.product = new ProductService(endpoints.product, orgId, storeId);
    this.auth = new AuthService(endpoints.auth, orgId, storeId);
    this.order = new OrderService(endpoints.order, orgId, storeId);
    this.user = new UserService(endpoints.user, orgId, storeId);
    this.payment = new PaymentService(endpoints.payment, orgId, storeId);
    this.crm = new CrmService(endpoints.crm, orgId, storeId);
    this.service = new ServiceManagementService(
      endpoints.service,
      orgId,
      storeId
    );
    this.warehouse = new WarehouseService(endpoints.warehouse, orgId, storeId);
    this.computing = new ComputingService(endpoints.computing, orgId, storeId);
    this.campaign = new CampaignService(endpoints.campaign, orgId, storeId);
    this.image = new ImageService(endpoints.image, orgId, storeId);
    this.paymentV2 = new PaymentServiceV2(endpoints.paymentV2, orgId, storeId);
    this.warehouseV2 = new WarehouseServiceV2(
      endpoints.warehouseV2,
      orgId,
      storeId
    );
    this.deepLinkVietQr = new DeepLinkVietQrService(
      endpoints.deepLinkVietQr,
      orgId,
      storeId
    );
    this.comhub = new ComhubService(endpoints.comhub, orgId, storeId);
    this.portal = new PortalService(endpoints.portal, orgId, storeId);
    this.upload = new UploadService(endpoints.upload, orgId, storeId);
    this.getImage = new GetImageService(endpoints.getImage, orgId, storeId);
    this.accounting = new AccountingService(
      endpoints.accounting,
      orgId,
      storeId
    );
    this.omnigateway = new OmnigatewayService(
      endpoints.omnigateway,
      orgId,
      storeId
    );
    this.authorization = new AuthorizationService(
      endpoints.authorization,
      orgId,
      storeId
    );
    this.zca = new ZcaService(endpoints.zca, orgId, storeId);
    this.cashbook = new CashbookService(endpoints.cashbook, orgId, storeId);
    // Initialize other services here
  }

  /**
   * Get all service instances for iteration
   * Uses caching to avoid recreating the array on every call
   */
  private getServiceInstances(): Array<{ name: string; instance: any }> {
    if (!this._serviceInstances) {
      this._serviceInstances = [
        { name: "product", instance: this.product },
        { name: "auth", instance: this.auth },
        { name: "order", instance: this.order },
        { name: "user", instance: this.user },
        { name: "payment", instance: this.payment },
        { name: "crm", instance: this.crm },
        { name: "service", instance: this.service },
        { name: "warehouse", instance: this.warehouse },
        { name: "computing", instance: this.computing },
        { name: "campaign", instance: this.campaign },
        { name: "image", instance: this.image },
        { name: "paymentV2", instance: this.paymentV2 },
        { name: "warehouseV2", instance: this.warehouseV2 },
        { name: "deepLinkVietQr", instance: this.deepLinkVietQr },
        { name: "comhub", instance: this.comhub },
        { name: "portal", instance: this.portal },
        { name: "upload", instance: this.upload },
        { name: "getImage", instance: this.getImage },
        { name: "accounting", instance: this.accounting },
        { name: "omnigateway", instance: this.omnigateway },
        { name: "authorization", instance: this.authorization },
        { name: "zca", instance: this.zca },
        { name: "cashbook", instance: this.cashbook },
      ];
    }
    return this._serviceInstances;
  }

  setToken(token: string) {
    this.token = token;
    // Set token for all services that have setToken method
    this.getServiceInstances().forEach(({ instance }) => {
      if (instance && typeof instance.setToken === "function") {
        instance.setToken(token);
      }
    });
  }
  setStoreId(storeId: string) {
    this.storeId = storeId;
    // Set storeId for all services that have setStoreId method
    this.getServiceInstances().forEach(({ instance }) => {
      if (instance && typeof instance.setStoreId === "function") {
        instance.setStoreId(storeId);
      }
    });
  }
  setOrgId(orgId: string) {
    this.orgId = orgId;
    // Set orgId for all services that have setOrgId method
    this.getServiceInstances().forEach(({ instance }) => {
      if (instance && typeof instance.setOrgId === "function") {
        instance.setOrgId(orgId);
      }
    });
  }
}
