import { gql } from "graphql-tag";

/**
 * Common product fields fragment
 * Used across multiple product queries to reduce duplication
 */
export const PRODUCT_CORE_FIELDS = gql`
  fragment ProductCoreFields on Product {
    id
    title
    description
    sku
    shortDescription
    weight
    width
    depth
    height
    vat
    qualify
    parentId
    handle
    price
    options
    optionsRelationship
    compareAtPrice
    featuredImage
    images
  }
`;

/**
 * Product attributes fragment
 */
export const PRODUCT_ATTRIBUTES = gql`
  fragment ProductAttributes on Product {
    productAttributes {
      attributeName
      attributeValue
    }
  }
`;

/**
 * Product variants fragment
 */
export const PRODUCT_VARIANTS = gql`
  fragment ProductVariants on Product {
    variants {
      id
      handle
      title
      price
      compareAtPrice
      options
      optionsIds
      featuredImage
      sku
    }
  }
`;

/**
 * Product feature types fragment
 */
export const PRODUCT_FEATURE_TYPES = gql`
  fragment ProductFeatureTypes on Product {
    featureTypes {
      id
      name
      values
      valuesFull {
        id
        name
      }
    }
  }
`;

/**
 * Product categories fragment
 */
export const PRODUCT_CATEGORIES = gql`
  fragment ProductCategories on Product {
    categories {
      id
      title
      handle
    }
  }
`;

/**
 * Product unit fragment
 */
export const PRODUCT_UNIT = gql`
  fragment ProductUnit on Product {
    unitDTO {
      id
      name
    }
  }
`;

/**
 * Complete product details fragment
 * Combines all product fragments for detailed queries
 */
export const PRODUCT_FULL_DETAILS = gql`
  fragment ProductFullDetails on Product {
    ...ProductCoreFields
    ...ProductAttributes
    ...ProductVariants
    ...ProductFeatureTypes
    ...ProductCategories
    ...ProductUnit
  }
  ${PRODUCT_CORE_FIELDS}
  ${PRODUCT_ATTRIBUTES}
  ${PRODUCT_VARIANTS}
  ${PRODUCT_FEATURE_TYPES}
  ${PRODUCT_CATEGORIES}
  ${PRODUCT_UNIT}
`;

/**
 * Simple product fields for list views
 */
export const PRODUCT_SIMPLE_FIELDS = gql`
  fragment ProductSimpleFields on Product {
    id
    title
    sku
    handle
    price
    compareAtPrice
    featuredImage
    available
    tags
    priceType
    salePolicy
    priceTypeName
    priceVaries
    subType
  }
`;
