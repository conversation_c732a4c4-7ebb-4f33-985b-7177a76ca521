export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  partyId: string;
  orgId: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  identityNumber: string;
  gender: string;
  birthDate: string;
  avatarUrl: string;
  accessToken: string;
  username: string;
  orgPermissionsMap: Record<string, any>; // Assuming it's a map of permissions
  orgPositionsMap: Record<string, any>; // Assuming it's a map of positions
  orgRolesMap: Record<string, any>; // Assuming it's a map of roles
}

export interface RegisterRequest {
  username: string;
  fullName: string;
  password: string;
  userIP: string;
}

export interface RegisterResponse {
  id: string;
  partyId: string;
  type: string;
  username: string;
  status: string;
  accessToken: string;
}

export interface SendSmsVerifyCodeResponse {
  id: string;
  code: string;
  username: string;
  timeExpired: string; // This should ideally be a Date object, but it depends on the format returned by your GraphQL server
}

export interface SmsVerifyCodeRequest {
  username: string;
  code: string;
}

export interface ResetPasswordRequest {
  username: string;
  newPassword: string;
  accessToken: string;
}

export interface ResetPasswordResponse {
  success: boolean;
}



export interface UpdateInfoRequest {
  orgId?: string | null;
  accessToken?: string | null;
  updateUserRequest: {
      fullName?: string | null;
      address?: string | null;
      gender?: string | null;
      birthDateLongTime?: string | null;
      birthDate?: string | null;
      email?: string | null;
      identityNumber?: string | null;
      phone?: string | null;
      imageUrl?: string | null;
      personalTitle?: string | null;
  };
  type?: string | null;
  password?: string | null;
}

export interface UpdateInfoResponse {
  partyId: string;
  fullName: string;
  email: string;
  phone: string;
  address: string;
  identityNumber: string;
  gender: string;
  birthDate: string;
  avatarUrl: string;
}
