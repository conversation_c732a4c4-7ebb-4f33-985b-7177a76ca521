import { Service } from "../serviceSDK";
import {
  GET_CAMPAIGN_ACTION_ACTIVE_NOW,
  GET_VOUCHERS,
  CHECK_VALID_VOUCHER,
  GET_CAMPAIGN_ACTIVE_NOW,
  GET_PROMOTION_PRODUCT_PRICE,
  GET_VOUCHER_AVAILABLE_FOR_CUSTOMER,
  SUGGEST_VOUCHER,
  GET_CAMPAIGN_ACTION_BY_ID,
  SEARCH_PRODUCT_GIFT_PROMOTION_RESPONSE,
} from "../../graphql/campaign/queries";
import { ADD_CUSTOMER_ID_INTO_VOUCHER } from "../../graphql/campaign/mutations";
import { SearchVoucherRequest } from "../../types/campaign";
export class CampaignService extends Service {
  /**
   * Constructs a new CampaignService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  async getCampaignActionActiveNow(
    customerId: string,
    campaignActionType: string
  ) {
    const query = GET_CAMPAIGN_ACTION_ACTIVE_NOW;
    const variables = {
      partyId: this.orgId,
      productStoreId: this.storeId,
      customerId,
      campaignActionType,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCampaignActionActiveNow;
    } catch (error) {
      console.log(`Error fetching getCampaignActionActiveNow: ${error}`);
      throw error;
    }
  }

  async searchVouchers(
    campaignId: String,
    campaignActionId: String,
    campaignActionType: String,
    customerId: String,
    excludeExpired: Boolean,
    pageNumber: number,
    pageSize: number
  ) {
    const query = GET_VOUCHERS;
    const variables = {
      partyId: this.orgId,
      storeId: this.storeId,
      campaignId,
      campaignActionId,
      campaignActionType,
      customerId,
      excludeExpired,
      pageNumber,
      pageSize,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.searchVoucher;
    } catch (error) {
      console.log(`Error fetching searchVoucher: ${error}`);
      throw error;
    }
  }
  async checkValidVoucher(customerId: string, voucherCode: string) {
    const query = CHECK_VALID_VOUCHER;
    const variables = {
      partyId: this.orgId,
      productStoreId: this.storeId,
      customerId,
      voucherCode,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.checkValidVoucher;
    } catch (error) {
      console.log(`Error fetching searchVoucher: ${error}`);
      throw error;
    }
  }
  async getCampaignActiveNow(campaignActionType: string, customerId: string) {
    const query = GET_CAMPAIGN_ACTIVE_NOW;
    const variables = {
      partyId: this.orgId,
      productStoreId: this.storeId,
      campaignActionType,
      customerId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCampaignActiveNow;
    } catch (error) {
      throw error;
    }
  }
  async getPromotionProductPrice(productId: String, productPrice: number) {
    const query = GET_PROMOTION_PRODUCT_PRICE;
    const variables = {
      partyId: this.orgId,
      productStoreId: this.storeId,
      productId,
      productPrice,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getPromotionProductPrice;
    } catch (error) {
      throw error;
    }
  }
  async getVoucherAvailableForCustomer(
    campaignId: string,
    campaignActionId: string,
    customerId: string,
    excludeExpired: Boolean,
    isPageAble: Boolean
  ) {
    const query = GET_VOUCHER_AVAILABLE_FOR_CUSTOMER;
    const variables = {
      partyId: this.orgId,
      storeId: this.storeId,
      campaignId,
      campaignActionId,
      customerId,
      excludeExpired,
      isPageAble,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getVoucherAvailableForCustomer;
    } catch (error) {
      throw error;
    }
  }
  async addCustomerToVoucher(
    voucherCode: string,
    userId: string,
    affiliateId: string
  ) {
    const query = ADD_CUSTOMER_ID_INTO_VOUCHER;
    const variables = {
      partyId: this.orgId,
      voucherCode,
      userId,
      affiliateId,
    };
    try {
      const response = await this.graphqlMutation(query, variables);
      return response.addCustomerIdIntoVoucher;
    } catch (error) {
      throw error;
    }
  }

  async suggestVoucher(customerId: string) {
    const query = SUGGEST_VOUCHER;
    const variables = {
      partyId: this.orgId,
      customerId: customerId,
      excludeExpired: true,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.suggestVoucher;
    } catch (error) {
      throw error;
    }
  }

  async getCampaignActionById(id: string) {
    const query = GET_CAMPAIGN_ACTION_BY_ID;
    const variables = {
      id,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCampaignActionById;
    } catch (error) {
      throw error;
    }
  }
  async searchProductGiftPromotionResponse(campaignActionId: string) {
    const query = SEARCH_PRODUCT_GIFT_PROMOTION_RESPONSE;
    const variables = {
      partyId: this.orgId,
      storeId: this.storeId,
      campaignActionId: campaignActionId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.searchProductGiftPromotionResponse;
    } catch (error) {
      throw error;
    }
  }
}
