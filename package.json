{"name": "@longvansoftware/storefront-js-client", "version": "2.9.7", "main": "dist/src/index.js", "types": "dist/src/index.d.ts", "files": ["dist/**/*.d.ts", "dist/**/*.js"], "directories": {"test": "jest"}, "scripts": {"test": "jest", "build": "tsc", "publish": "npm run build && npm publish"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@apollo/client": "3.9.11", "apollo-boost": "^0.4.9", "axios": "^1.6.8", "cross-fetch": "^4.0.0", "crypto-js": "^4.2.0", "graphql": "^15.8.0", "graphql-request": "^6.1.0", "graphql-tag": "^2.12.6", "react": "^18.2.0", "ts-node": "^10.9.2"}, "devDependencies": {"@types/axios": "^0.14.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/node": "^20.12.7", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}, "description": ""}