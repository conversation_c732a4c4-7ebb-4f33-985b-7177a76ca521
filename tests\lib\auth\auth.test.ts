// tests/lib/product/product.test.ts

import sdk from "../../setup";

describe("AuthService", () => {
  let authService: any;

  beforeAll(() => {
    authService = sdk.auth;
  });

  it("login integration test", async () => {
    const response = await authService.login({
      username: "admin",
      password: "123456",
    });
    console.log(response);
    sdk.setToken(response.accessToken);
    expect(response).toHaveProperty("accessToken");
  });

  // it("register integration test", async () => {
  //   const response = await authService.register("username", "password");
  //   expect(response).toHaveProperty("success", true);
  // });

  // Add more integration tests for other methods...
});
