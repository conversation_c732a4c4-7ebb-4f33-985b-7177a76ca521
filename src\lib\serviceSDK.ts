// src/service.ts

import {
  ApolloClient,
  InMemoryCache,
  gql,
  NormalizedCacheObject,
  HttpLink,
} from "@apollo/client";
import axios from "axios";
import { DocumentNode } from "graphql";
import fetch from "cross-fetch";

/**
 * Standardized error class for SDK operations
 */
export class SDKError extends Error {
  constructor(
    message: string,
    public type:
      | "GRAPHQL_ERROR"
      | "NETWORK_ERROR"
      | "REST_API_ERROR"
      | "UNKNOWN_ERROR",
    public originalError?: any
  ) {
    super(message);
    this.name = "SDKError";
  }
}

/**
 * Apollo Client factory to reuse clients for same endpoints
 * This reduces memory usage by sharing clients across services
 */
class ApolloClientFactory {
  private static clients = new Map<
    string,
    ApolloClient<NormalizedCacheObject>
  >();

  static getClient(endpoint: string): ApolloClient<NormalizedCacheObject> {
    if (!this.clients.has(endpoint)) {
      this.clients.set(
        endpoint,
        new ApolloClient({
          link: new HttpLink({ uri: endpoint, fetch }),
          cache: new InMemoryCache({
            // Optimize cache configuration
            typePolicies: {
              Query: {
                fields: {
                  // Cache products for 5 minutes
                  getSimpleProducts: {
                    merge: false,
                  },
                  getProductById: {
                    merge: false,
                  },
                },
              },
            },
          }),
          defaultOptions: {
            query: {
              // Use cache-first for better performance, fallback to network
              fetchPolicy: "cache-first",
              errorPolicy: "all",
            },
            mutate: {
              errorPolicy: "all",
            },
          },
        })
      );
    }
    return this.clients.get(endpoint)!;
  }

  // Method to clear cache for specific endpoint (useful for testing)
  static clearClient(endpoint: string): void {
    this.clients.delete(endpoint);
  }

  // Method to clear all clients (useful for cleanup)
  static clearAllClients(): void {
    this.clients.clear();
  }
}
export class Service {
  protected token: string | null = null;
  protected client: ApolloClient<NormalizedCacheObject>;
  protected orgId: string;
  protected storeId: string;
  protected endpoint: string;

  constructor(endpoint: string, orgId: string, storeId: string) {
    // Use factory to get shared Apollo Client instance
    this.client = ApolloClientFactory.getClient(endpoint);
    this.orgId = orgId;
    this.storeId = storeId;
    this.endpoint = endpoint;
  }
  setToken(token: string) {
    this.token = token;
  }
  setStoreId(storeId: string) {
    this.storeId = storeId;
  }
  setOrgId(orgId: string) {
    this.orgId = orgId;
  }
  /**
   * Standardized error handling for Apollo Client
   * Returns a structured error object instead of just string
   */
  private handleApolloError(error: any): SDKError {
    if (error.graphQLErrors && error.graphQLErrors.length > 0) {
      return new SDKError(
        error.graphQLErrors.map((err: any) => err.message).join(", "),
        "GRAPHQL_ERROR",
        error.graphQLErrors
      );
    }
    if (error.networkError) {
      return new SDKError(
        `Network Error: ${error.networkError.message}`,
        "NETWORK_ERROR",
        error.networkError
      );
    }
    return new SDKError(
      error.message || "An unknown error occurred",
      "UNKNOWN_ERROR",
      error
    );
  }

  /**
   * Unified GraphQL query method with standardized error handling
   */
  protected async graphqlQuery(
    query: DocumentNode,
    variables: any,
    includeAuth: boolean = false,
    includePartnerId: boolean = true
  ) {
    try {
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      if (includePartnerId) {
        headers.partnerId = this.orgId;
      }

      if (includeAuth && this.token) {
        headers.Authorization = `Bearer ${this.token}`;
      }

      const { data, errors } = await this.client.query({
        query: gql`
          ${query}
        `,
        variables,
        context: {
          method: "POST",
          headers,
        },
      });

      if (errors && errors.length > 0) {
        throw this.handleApolloError({ graphQLErrors: errors });
      }
      return data;
    } catch (error: any) {
      throw this.handleApolloError(error);
    }
  }

  /**
   * Unified GraphQL mutation method with standardized error handling
   */
  protected async graphqlMutation(
    mutation: DocumentNode,
    variables: any,
    includeAuth: boolean = false,
    includePartnerId: boolean = true
  ) {
    try {
      const headers: Record<string, string> = {
        "Content-Type": "application/json",
      };

      if (includePartnerId) {
        headers.partnerId = this.orgId;
      }

      if (includeAuth && this.token) {
        headers.Authorization = `Bearer ${this.token}`;
      }

      const { data, errors } = await this.client.mutate({
        mutation: gql`
          ${mutation}
        `,
        variables,
        context: {
          method: "POST",
          headers,
        },
      });

      if (errors && errors.length > 0) {
        throw this.handleApolloError({ graphQLErrors: errors });
      }
      return data;
    } catch (error: any) {
      throw this.handleApolloError(error);
    }
  }
  protected async restApiCallWithToken(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    headers?: any
  ) {
    try {
      const modifiedHeaders = {
        ...headers,
        PartnerId: this.orgId,
        Authorization: "Bearer " + this.token,
        "X-Ecomos-Access-Token": this.token,
        "Partner-Id": this.orgId,
      };
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
        headers: modifiedHeaders,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  }
  protected async restApiCallWithNoToken(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any,
    headers?: any
  ) {
    try {
      const modifiedHeaders = {
        ...headers,
        Partnerid: this.orgId,
      };
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
        headers: modifiedHeaders,
      });
      return response.data;
    } catch (error) {
      console.log(`Error in restApiCallWithNoToken: ${error}`);
      throw error;
    }
  }
  // call api no header
  protected async restApiCallWithNoHeader(
    path: string,
    method: "GET" | "POST" | "PUT" | "DELETE",
    data?: any
  ) {
    try {
      const response = await axios({
        url: this.endpoint + path,
        method,
        data,
      });
      return response.data;
    } catch (error) {
      throw new SDKError(`REST API Error: ${error}`, "REST_API_ERROR", error);
    }
  }
  //

  /**
   * @deprecated Use graphqlQuery with includeAuth=true instead
   */
  protected async graphqlQueryV2(query: DocumentNode, variables: any) {
    return this.graphqlQuery(query, variables, true);
  }
  /**
   * @deprecated Use graphqlMutation with includeAuth=true instead
   */
  protected async graphqlMutationV2(mutation: DocumentNode, variables: any) {
    return this.graphqlMutation(mutation, variables, true);
  }
  /**
   * @deprecated Use graphqlQuery with includeAuth=true, includePartnerId=false instead
   */
  protected async graphqlQueryV3(query: DocumentNode, variables: any) {
    return this.graphqlQuery(query, variables, true, false);
  }
  /**
   * @deprecated Use graphqlMutation with includeAuth=true, includePartnerId=false instead
   */
  protected async graphqlMutationV3(mutation: DocumentNode, variables: any) {
    return this.graphqlMutation(mutation, variables, true, false);
  }
}
