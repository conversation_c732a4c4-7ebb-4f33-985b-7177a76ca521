import { gql } from "graphql-tag";

export const LOGIN_MUTATION = gql`
  mutation Login($loginRequest: LoginRequest!) {
    login(loginRequest: $loginRequest) {
      partyId
      orgId
      fullName
      email
      phone
      address
      identityNumber
      gender
      birthDate
      avatarUrl
      accessToken
      username
      orgPermissionsMap
      orgPositionsMap
      orgRolesMap
    }
  }
`;

export const REGISTER_MUTATION = gql`
  mutation Register($orgId: String!, $registerRequest: RegisterRequest!) {
    register(orgId: $orgId, registerRequest: $registerRequest) {
      id
      partyId
      type
      username
      status
      accessToken
    }
  }
`;

export const SEND_SMS_VERIFY_CODE_MUTATION = gql`
  mutation SendSmsVerifyCode($orgId: String!, $username: String!) {
    sendSmsVerifyCode(orgId: $orgId, username: $username) {
      id
      code
      username
      timeExpired
    }
  }
`;

export const VERIFY_CODE_MUTATION = gql`
  mutation VerifyCode($orgId: String!, $verifyCodeRequest: VerifyCodeRequest!) {
    verifyCode(orgId: $orgId, verifyCodeRequest: $verifyCodeRequest)
  }
`;

export const RESET_PASSWORD_MUTATION = gql`
  mutation ResetPassword(
    $orgId: String!
    $username: String!
    $newPassword: String!
    $accessToken: String!
  ) {
    resetPassword(
      orgId: $orgId
      username: $username
      newPassword: $newPassword
      accessToken: $accessToken
    )
  }
`;

export const UPDATE_INFO_MUTATION = gql`
  mutation UpdateInfo(
    $orgId: String
    $accessToken: String
    $updateUserRequest: UpdateUserRequest
    $type: String
    $password: String
  ) {
    updateInfo(
      orgId: $orgId
      accessToken: $accessToken
      updateUserRequest: $updateUserRequest
      type: $type
      password: $password
    ) {
      partyId
      fullName
      email
      phone
      address
      identityNumber
      gender
      birthDate
      avatarUrl
    }
  }
`;
export const UPDATE_PASSWORD_MUTATION = `
  mutation UpdatePassword($orgId: String!, $accessToken: String!, $currentPassword: String!, $newPassword: String!) {
    updatePassword(orgId: $orgId, accessToken: $accessToken, currentPassword: $currentPassword, newPassword: $newPassword)
  }
`;

export const UPDATE_PROFILE_MUTATION = gql`
  mutation UpdateProfile(
    $userLoginId: String!
    $name: String!
    $phone: String!
    $email: String
  ) {
    updateProfile(
      userLoginId: $userLoginId
      name: $name
      phone: $phone
      email: $email
    )
  }
`;

export const LINKING_USER_LOGIN_AND_USER_DETAIL_MUTATION = gql`
  mutation LinkingUserLoginAndUserDetail(
    $userLoginId: String!
    $partyId: String!
  ) {
    linkingUserLoginAndUserDetail(userLoginId: $userLoginId, partyId: $partyId)
  }
`;



export const CREATE_USER_DETAIL_MUTATION = gql`
  mutation CreateUserDetail($userLoginId: String!, $partnerId: String!) {
    createUserDetail(userLoginId: $userLoginId, partnerId: $partnerId) {
      partyId
      orgId
      fullName
      email
      phone
      address
      identityNumber
      gender
      birthDate
      avatarUrl
      accessToken
      username
      readyV2
      orgPermissionsMap
      orgPositionsMap
      orgRolesMap
    }
  }
`;
