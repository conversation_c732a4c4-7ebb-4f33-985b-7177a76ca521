// lib/auth/index.ts
import { gql } from "@apollo/client";
import { Service } from "../serviceSDK";
import {
  LOGIN_MUTATION,
  REGISTER_MUTATION,
  UPDATE_PROFILE_MUTATION,
  LINKING_USER_LOGIN_AND_USER_DETAIL_MUTATION,
  CREATE_USER_DETAIL_MUTATION,
  SEND_SMS_VERIFY_CODE_MUTATION,
  VERIFY_CODE_MUTATION,
  RESET_PASSWORD_MUTATION,
} from "../../graphql/auth/mutations";
import { LoginRequest, LoginResponse, RegisterRequest } from "../../types/auth";
import {
  CHECK_USERNAME_EXISTED,
  GET_USER_DETAIL,
  GET_USER_LOGIN_BY_TOKEN,
  GET_USER_LOGIN_BY_USER_LOGIN_ID,
  LOGIN_FACEBOOK,
  LOGIN_GOOGLE,
  LOGIN_ZALO,
} from "../../graphql/auth/queries";

/**
 * Represents the authentication service.
 */
export class AuthService extends Service {
  /**
   * Constructs a new ProductService instance.
   * @param endpoint - The endpoint URL for the service.
   * @param orgId - The organization ID.
   * @param storeId - The store ID.
   */
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  /**
   * Logs in a user with the provided login request.
   * @param loginRequest - The login request object.
   * @returns A promise that resolves to the login response.
   */
  async login(loginRequest: LoginRequest): Promise<LoginResponse> {
    const variables = {
      loginRequest: {
        orgId: this.orgId,
        ...loginRequest,
      },
    };
    const data = await this.graphqlMutation(LOGIN_MUTATION, variables);
    return data.login;
  }

  /**
   * Registers a new user with the provided register request.
   * @param registerRequest - The register request object.
   * @returns A promise that resolves when the registration is successful.
   */
  public async register(registerRequest: RegisterRequest): Promise<void> {
    const variables = { registerRequest };
    await this.graphqlMutation(REGISTER_MUTATION, variables);
  }

  async getUserDetail(accessToken: string) {
    const query = GET_USER_DETAIL;
    const variables = {
      orgId: this.orgId,
      accessToken,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getUserDetail;
    } catch (error) {
      console.log(`Error in getUserDetail: ${error}`);
      throw error;
    }
  }

  /**
   * Retrieves user login information using the provided access token.
   *
   * @param {string} accessToken - The access token used to authenticate the request.
   * @returns {Promise<any>} A promise that resolves to the user login information.
   * @throws Will throw an error if the GraphQL query fails.
   */
  async getUserLoginByToken(accessToken: string): Promise<any> {
    const query = GET_USER_LOGIN_BY_TOKEN;
    const variables = {
      accessToken,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getUserLoginByToken;
    } catch (error) {
      console.log(`Error in getUserLoginByToken: ${error}`);
      throw error;
    }
  }

  async updateProfile(
    userLoginId: string,
    name: string,
    phone: string,
    email: string
  ) {
    const variables = {
      userLoginId,
      name,
      phone,
      email,
    };

    try {
      const response = await this.graphqlMutation(
        UPDATE_PROFILE_MUTATION,
        variables
      );
      return response.updateProfile;
    } catch (error) {
      console.log(`Error in updateProfile: ${error}`);
      throw error;
    }
  }
  async getUserLoginByUserLoginId(userLoginId: string): Promise<any> {
    const query = GET_USER_LOGIN_BY_USER_LOGIN_ID;
    const variables = {
      userLoginId,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getUserLoginByUserLoginId;
    } catch (error) {
      console.log(`Error in getUserLoginByUserLoginId: ${error}`);
      throw error;
    }
  }

  async checkUsernameExisted(username: string): Promise<any> {
    const variables = {
      username,
      orgId: this.orgId,
    };

    try {
      const response = await this.graphqlQuery(
        CHECK_USERNAME_EXISTED,
        variables
      );
      return response.checkUsernameExisted;
    } catch (error) {
      console.log(`Error in checkUsernameExisted: ${error}`);
      throw error;
    }
  }
  async linkingUserLoginAndUserDetail(userLoginId: string, partyId: string) {
    const variables = {
      userLoginId,
      partyId,
    };

    try {
      const response = await this.graphqlMutation(
        LINKING_USER_LOGIN_AND_USER_DETAIL_MUTATION,
        variables
      );
      return response.linkingUserLoginAndUserDetail;
    } catch (error) {
      console.log(`Error in linkingUserLoginAndUserDetail: ${error}`);
      throw error;
    }
  }

  async createUserDetail(userLoginId: string) {
    const variables = {
      userLoginId,
      partnerId: this.orgId,
    };

    try {
      const response = await this.graphqlMutation(
        CREATE_USER_DETAIL_MUTATION,
        variables
      );
      return response.createUserDetail;
    } catch (error) {
      console.log(`Error in createUserDetail: ${error}`);
      throw error;
    }
  }

  //   mutation VerifyCode {
  //     sendSmsVerifyCode(orgId: "FYP", username: "0971879660") {
  //         id
  //         code
  //         username
  //         timeExpired
  //     }
  // }
  async sendSmsVerifyCode(username: string) {
    const query = SEND_SMS_VERIFY_CODE_MUTATION;
    const variables = {
      orgId: this.orgId,
      username,
    };
    try {
      const response = await this.graphqlMutation(query, variables);
      return response.sendSmsVerifyCode;
    } catch (error) {
      console.log(`Error in sendSmsVerifyCode: ${error}`);
      throw error;
    }
  }

  //   mutation VerifyCode {
  //     verifyCode(
  //         orgId: "FYP"
  //         verifyCodeRequest: { username: "0971879660", code: "999999" }
  //     )
  // }
  async verifyCode(username: string, code: string) {
    const query = VERIFY_CODE_MUTATION;
    const variables = {
      orgId: this.orgId,
      verifyCodeRequest: { username, code },
    };
    try {
      const response = await this.graphqlMutation(query, variables);
      return response.verifyCode;
    } catch (error) {
      console.log(`Error in verifyCode: ${error}`);
      throw error;
    }
  }

  async resetPassword(
    username: string,
    newPassword: string,
    accessToken: string
  ) {
    const query = RESET_PASSWORD_MUTATION;
    const variables = {
      orgId: this.orgId,
      username,
      newPassword,
      accessToken,
    };
    try {
      const response = await this.graphqlMutation(query, variables);
      return response.resetPassword;
    } catch (error) {
      console.log(`Error in resetPassword: ${error}`);
      throw error;
    }
  }

  async loginGoogle(redirectUrl: string) {
    const query = LOGIN_GOOGLE;
    const variables = {
      orgId: this.orgId,
      type: "login",
      redirectUrl,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.loginGoogle;
    } catch (error) {
      console.log(`Error in loginGoogle: ${error}`);
      throw error;
    }
  }

  async loginFacebook(redirectUrl: string) {
    const query = LOGIN_FACEBOOK;
    const variables = {
      orgId: this.orgId,
      type: "login",
      redirectUrl,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.loginFacebook;
    } catch (error) {
      console.log(`Error in loginFacebook: ${error}`);
      throw error;
    }
  }

  /**
   * Logs in a user via Zalo by executing a GraphQL query.
   *
   * @param redirectUrl - The URL to redirect the user to after login.
   * @returns A promise that resolves to the result of the `loginZalo` GraphQL query.
   * @throws Will throw an error if the GraphQL query fails.
   */
  async loginZalo(redirectUrl: string) {
    const query = LOGIN_ZALO;
    const variables = {
      orgId: this.orgId,
      type: "login",
      redirectUrl,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.loginZalo;
    } catch (error) {
      console.log(`Error in loginZalo: ${error}`);
      throw error;
    }
  }
}
