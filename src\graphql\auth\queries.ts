import { gql } from "graphql-tag";

export const GET_USER_DETAIL = gql`
  query GetUserDetail($orgId: String!, $accessToken: String!) {
    getUserDetail(orgId: $orgId, accessToken: $accessToken) {
      partyId
      orgId
      fullName
      email
      phone
      address
      identityNumber
      gender
      birthDate
      avatarUrl
      accessToken
      username
      orgPermissionsMap
      orgPositionsMap
      orgRolesMap
    }
  }
`;

export const GET_USER_LOGIN_BY_TOKEN = gql`
  query GetUserLoginByToken($accessToken: String!) {
    getUserLoginByToken(accessToken: $accessToken) {
      id
      status
      partyId
      userLoginId
    }
  }
`;

// query GetUserLoginByUserLoginId {
//   getUserLoginByUserLoginId(userLoginId: "0971879660") {
//       id
//       userLoginId
//       accessToken
//       status
//       partyId
//   }
// }

export const GET_USER_LOGIN_BY_USER_LOGIN_ID = gql`
  query GetUserLoginByUserLoginId($userLoginId: String!) {
    getUserLoginByUserLoginId(userLoginId: $userLoginId) {
      id
      userLoginId
      accessToken
      status
      partyId
    }
  }
`;

export const CHECK_USERNAME_EXISTED = gql`
  query CheckUsernameExisted($username: String!, $orgId: String!) {
    checkUsernameExisted(username: $username, orgId: $orgId)
  }
`;


export const LOGIN_GOOGLE = gql`
  query LoginGoogle($orgId: String!, $type: String!, $redirectUrl: String!) {
    loginGoogle(orgId: $orgId, type: $type, redirectUrl: $redirectUrl)
  }
`;

export const LOGIN_FACEBOOK = gql`
  query LoginFacebook($orgId: String!, $type: String!, $redirectUrl: String!) {
    loginFacebook(orgId: $orgId, type: $type, redirectUrl: $redirectUrl)
  }
`;

export const LOGIN_ZALO = gql`
  query LoginZalo($orgId: String!, $type: String!, $redirectUrl: String!) {
    loginZalo(orgId: $orgId, type: $type, redirectUrl: $redirectUrl)
  }
`;