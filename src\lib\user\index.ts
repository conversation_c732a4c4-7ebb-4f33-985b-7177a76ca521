import { Service } from "../serviceSDK";
import {
  GET_CUSTOMER_BY_ID,
  GET_PERSON_BY_IDS_QUERY,
  SEARCH_COMPANY,
  SEARCH_CUSTOMER,
  SEARCH_EMPLOYEES,
  GET_POSTIONS_BY_EMPLOYEES_ID,
  GET_STORE_CHANEL_IDS_BY_EMPLOYESS_ID,
  GET_EMPLOYEES_BY_STORE_CHANEL_ID,
  GET_COMPANY_BY_CONTACT_INFO_ID,
  GET_CONTACT_INFOS_BY_COMPANY_ID,
  GET_PROVINCES,
  GET_DISTRICTS,
  GET_WARDS,
  GET_PERSON_BY_PARTY_ID,
  GET_VAT_INFO_BY_OWNER_PARTYID,
} from "../../graphql/user/queries";
import {
  createCompanyRequest,
  createCustomerRequest,
  dataCustomerRequest,
  updateCustomerRequest,
  customerQuery,
  searchCustomersResponse,
  employeesQuery,
} from "../../types/user";
import {
  CREATE_COMPANY,
  CREATE_CUSTOMER_V2,
  UPDATE_COMPANY_INFOR,
  UPDATE_CUSTOMER_V2,
  CREATE_VAT_INFO,
} from "../../graphql/user/mutations";

export class UserService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }

  async getPersonByPartyIds(partyIds: string[]) {
    const query = GET_PERSON_BY_IDS_QUERY;
    const variables = { partyIds };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getPersonByPartyIds;
    } catch (error) {
      console.log(`Error in getPersonByPartyIds: ${error}`);
      throw error;
    }
  }

  async createCompany(payload: createCompanyRequest, createdBy: string) {
    const mutation = CREATE_COMPANY;
    const variables = {
      orgId: this.orgId,
      createCompanyRequest: payload,
      createTaxCodeRequest: null,
      createdBy,
    };

    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.createCompany;
    } catch (error) {
      console.log(`Error in createCompany: ${error}`);
      throw error;
    }
  }

  async updateCompanyInfo(
    id: string,
    fieldName: string,
    valueUpdate: string,
    updatedBy: string
  ) {
    const mutation = UPDATE_COMPANY_INFOR;
    const variables = {
      orgId: this.orgId,
      id,
      fieldName,
      valueUpdate,
      updatedBy,
    };

    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateCompanyInfo;
    } catch (error) {
      console.log(`Error in updateCompanyInfo: ${error}`);
      throw error;
    }
  }

  async updateCustomer(
    id: string,
    customerItem: updateCustomerRequest,
    updatedBy: string
  ) {
    const mutation = UPDATE_CUSTOMER_V2;
    const variables = {
      tenantId: this.orgId,
      id,
      updateCustomerRequest: customerItem,
      updatedBy,
    };

    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.updateCustomerV2;
    } catch (error) {
      console.log(`Error in updateCustomerV2: ${error}`);
      throw error;
    }
  }

  async getCustomerById(id: string) {
    const query = GET_CUSTOMER_BY_ID;
    const variables = {
      id,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCustomerById;
    } catch (error) {
      console.log(`Error in getCustomerById: ${error}`);
      throw error;
    }
  }

  async searchCompany(keyword: string, limit: number) {
    const query = SEARCH_COMPANY;
    const variables = {
      keyword,
      orgId: this.orgId,
      limit,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.searchCompany;
    } catch (error) {
      console.log(`Error in searchCompany: ${error}`);
      throw error;
    }
  }

  async createCustomerV2(
    createCustomerRequest: createCustomerRequest,
    createdBy: string
  ) {
    const mutation = CREATE_CUSTOMER_V2;
    const variables = {
      name: createCustomerRequest?.name,
      phone: createCustomerRequest?.phone || "",
      email: createCustomerRequest?.email || "",
      birthDate: createCustomerRequest?.birthDate || null,
      tenantId: this.orgId,
      createdBy: createdBy,
    };

    try {
      const response = await this.graphqlMutation(mutation, variables);
      return response.createCustomerV2;
    } catch (error) {
      console.log(`Error in createCustomerV2: ${error}`);
      throw error;
    }
  }

  //PERSON/COMPANY
  async createCustomer(
    dataCustomerRequest: dataCustomerRequest,
    createdBy: string,
    type: string
  ) {
    const dataCreateCustomer = {
      name: dataCustomerRequest?.name || "",
      phone: dataCustomerRequest?.phone || "",
      email: dataCustomerRequest?.email || "",
      birthDate: dataCustomerRequest?.birthDate || null,
    };

    const dataCreateCompany = {
      name: dataCustomerRequest?.name || "",
      phone: dataCustomerRequest?.phone || "",
      email: dataCustomerRequest?.email || "",
      address: dataCustomerRequest?.address || "",
    };

    if (type == "PERSON") {
      const dataPERSON = await this.createCustomerV2(
        dataCreateCustomer,
        createdBy
      );
      return dataPERSON;
    } else if (type == "COMPANY") {
      const dataCOMPANY = await this.createCompany(
        dataCreateCompany,
        createdBy
      );
      return dataCOMPANY;
    }
  }

  async searchCustomer(
    searchParams: customerQuery
  ): Promise<searchCustomersResponse> {
    const query = SEARCH_CUSTOMER;
    const variables = {
      keyword: searchParams.keyword,
      type: searchParams.type,
      startCreatedDate: searchParams.startCreatedDate,
      endCreatedDate: searchParams.endCreatedDate,
      memberLevel: searchParams.memberLevel,
      partnerId: this.orgId,
      currentPage: searchParams.currentPage,
      pageSize: searchParams.pageSize,
    };

    try {
      const response = await this.graphqlQuery(query, variables);
      return response.searchCustomers;
    } catch (error) {
      console.log(`Error in searchCustomer: ${error}`);
      throw error;
    }
  }

  async searchEmployees(searchParams: employeesQuery) {
    const query = SEARCH_EMPLOYEES;
    const variables = {
      keyword: searchParams.keyword,
      partnerId: this.orgId,
      positionShortName: searchParams.positionShortName,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.searchEmployees;
    } catch (error) {
      console.log(`Error inseachEmployess: ${error}`);
      throw error;
    }
  }

  async getPositionsByEmployeeId(employeeId: string) {
    const query = GET_POSTIONS_BY_EMPLOYEES_ID;
    const variables = {
      employeeId,
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getPositionsByEmployeeId;
    } catch (error) {
      console.log(`Error in getPositionsByEmployeeId: ${error}`);
      throw error;
    }
  }
  async getStoreChannelIdsByEmployeeId(employeeId: string) {
    const query = GET_STORE_CHANEL_IDS_BY_EMPLOYESS_ID;
    const variables = {
      employeeId,
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getStoreChannelIdsByEmployeeId;
    } catch (error) {
      console.log(`Error in getStoreChannelIdsByEmployeeId: ${error}`);
      throw error;
    }
  }
  async getEmployeesByStoreChannelId() {
    const query = GET_EMPLOYEES_BY_STORE_CHANEL_ID;
    const variables = {
      storeChannelId: this.storeId,
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getEmployeesByStoreChannelId;
    } catch (error) {
      console.log(`Error in getEmployeesByStoreChannelId: ${error}`);
      throw error;
    }
  }
  async getCompanyByContactInfoId(contactId: string) {
    const query = GET_COMPANY_BY_CONTACT_INFO_ID;
    const variables = {
      contactId,
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getCompanyByContactInfoId;
    } catch (error) {
      console.log(`Error in getCompanyByContactInfoId: ${error}`);
      throw error;
    }
  }
  async getContactInfosByCompanyId(companyId: string) {
    const query = GET_CONTACT_INFOS_BY_COMPANY_ID;
    const variables = {
      companyId,
      partnerId: this.orgId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getContactInfosByCompanyId;
    } catch (error) {
      console.log(`Error in getContactInfosByCompanyId: ${error}`);
      throw error;
    }
  }
  async getProvinces() {
    const query = GET_PROVINCES;
    const variables = {};
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getProvinces;
    } catch (error) {
      console.log(`Error in getProvinces: ${error}`);
      throw error;
    }
  }
  async getDistricts(provinceId: string) {
    const query = GET_DISTRICTS;
    const variables = {
      provinceId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getDistricts;
    } catch (error) {
      console.log(`Error in getDistricts: ${error}`);
      throw error;
    }
  }
  async getWards(districtId: string) {
    const query = GET_WARDS;
    const variables = {
      districtId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getWards;
    } catch (error) {
      console.log(`Error in getWards: ${error}`);
      throw error;
    }
  }
  async getPersonByPartyId(partyId: string) {
    const query = GET_PERSON_BY_PARTY_ID;
    const variables = {
      partyId,
    };
    try {
      const response = await this.graphqlQueryV2(query, variables);
      return response.getPersonByPartyId;
    } catch (error) {
      throw error;
    }
  }
  async getVatInfoByOwnerPartyId(ownerPartyId: string) {
    const query = GET_VAT_INFO_BY_OWNER_PARTYID;
    const variables = {
      ownerPartyId,
    };
    try {
      const response = await this.graphqlQuery(query, variables);
      return response.getVatInfoByOwnerPartyId;
    } catch (error) {
      throw error;
    }
  }
  async createVatInfo(
    company: string,
    taxCode: string,
    invoiceReceiveEmail1: string,
    ownerPartyId: string,
    address: string,
    createdBy: string
  ) {
    const mutation = CREATE_VAT_INFO;
    const variables = {
      company,
      taxCode,
      invoiceReceiveEmail1,
      ownerPartyId,
      address,
      createdBy,
    };
    try {
      const response = await this.graphqlMutation(mutation, variables)
      return response.createVatInfo
    } catch (error) {
      throw error
    }
  }
}
