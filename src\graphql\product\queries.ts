import { gql } from "graphql-tag";
// export const GET_PRODUCT_BY_ID_QUERY = gql`
//   query GetProductById(
//     $partnerId: String!
//     $storeChannel: String!
//     $productId: String!
//   ) {
//     getProductById(
//       partnerId: $partnerId
//       storeChannel: $storeChannel
//       productId: $productId
//     ) {
//       title
//       id
//       description
//       sku
//       shortDescription
//       weight
//       width
//       height
//       vat
//       unit
//       qualify
//       parentId
//       handle
//       price
//       priceVaries
//       available
//       options
//       compareAtPrice
//       featuredImage
//       images
//       createdAt
//       favorite
//       productAttributes {
//         id
//         productId
//         attributeName
//         attributeValue
//       }
//       variants {
//         id
//         handle
//         title
//         price
//         weight
//         width
//         depth
//         height
//         compareAtPrice
//         available
//         option1
//         option2
//         option3
//         options
//         weightUnit
//         optionsIds
//         featuredImage
//         sku
//         qualify
//         featureTypeDTOS {
//           id
//           name
//           position
//           values
//           required
//           valuesFull
//           enable
//         }
//       }
//       featureTypes {
//         id
//         name
//         position
//         values
//         required
//         valuesFull
//         enable
//       }
//       categories {
//         id
//         title
//         handle
//         description
//       }
//     }
//   }
// `;

export const GET_PRODUCT_BY_ID_QUERY = gql`
  query GetProductById(
    $partnerId: String!
    $storeChannel: String!
    $productId: String!
  ) {
    getProductById(
      partnerId: $partnerId
      storeChannel: $storeChannel
      productId: $productId
    ) {
      id
      title
      description
      sku
      shortDescription
      weight
      width
      depth
      height
      vat
      qualify
      parentId
      handle
      price
      options
      optionsRelationship
      compareAtPrice
      featuredImage
      images
      productAttributes {
        attributeName
        attributeValue
      }
      variants {
        id
        handle
        title
        price
        compareAtPrice
        options
        optionsIds
        featuredImage
        sku
      }
      featureTypes {
        id
        name
        values
        valuesFull {
          id
          name
        }
      }
      categories {
        id
        title
        handle
      }
      unitDTO {
        id
        name
      }
    }
  }
`;

export const GET_PRODUCT_BY_SLUG_QUERY = gql`
  query GetProductByHandle(
    $partnerId: String!
    $storeChannel: String!
    $handle: String!
  ) {
    getProductByHandle(
      partnerId: $partnerId
      storeChannel: $storeChannel
      handle: $handle
    ) {
      id
      title
      description
      sku
      shortDescription
      weight
      width
      depth
      height
      vat
      qualify
      parentId
      handle
      price
      options
      optionsRelationship
      compareAtPrice
      featuredImage
      images
      productAttributes {
        attributeName
        attributeValue
      }
      variants {
        id
        handle
        title
        price
        compareAtPrice
        options
        optionsIds
        featuredImage
        sku
      }
      featureTypes {
        id
        name
        values
      }
      categories {
        id
        title
        handle
      }
      unitDTO {
        id
        name
      }
    }
  }
`;

export const GET_SIMPLE_PRODUCTS_QUERY = gql`
  query GetSimpleProducts(
    $partnerId: String!
    $storeChannel: String!
    $category: String
    $product: String
    $sku: String
    $tag: String
    $priceFrom: BigDecimal
    $priceTo: BigDecimal
    $status: String
    $productType: String
    $subType: String
    $sortOrder: String
    $sortBy: String
    $brandId: String
    $keyword: String
    $display: String
    $onlyPromotion: Boolean
    $currentPage: Int
    $maxResult: Int
  ) {
    getSimpleProducts(
      partnerId: $partnerId
      storeChannel: $storeChannel
      category: $category
      product: $product
      sku: $sku
      tag: $tag
      priceFrom: $priceFrom
      priceTo: $priceTo
      status: $status
      productType: $productType
      subType: $subType
      sortOrder: $sortOrder
      sortBy: $sortBy
      brandId: $brandId
      keyword: $keyword
      display: $display
      onlyPromotion: $onlyPromotion
      currentPage: $currentPage
      maxResult: $maxResult
    ) {
      total
      currentPage
      maxResult
      totalPage
      data {
        id
        title
        sku
        shortDescription
        description
        subType
        vat
        qualify
        parentId
        handle
        price
        compareAtPrice
        priceType
        priceTypeName
        featuredImage
        optionsRelationship
        images
      }
    }
  }
`;
export const GET_CATEGORIES_QUERY = gql`
  query GetCategories(
    $partnerId: String!
    $storeChannel: String!
    $typeBuild: String!
    $level: Int!
  ) {
    getCategories(
      partnerId: $partnerId
      storeChannel: $storeChannel
      typeBuild: $typeBuild
      level: $level
    ) {
      id
      title
      image
      icon
      parentId
      level
      handle
      description
      child {
        id
        title
        image
        icon
        parentId
        level
        handle
        description
      }
    }
  }
`;

export const GET_CATEGORY_BY_HANDLE_QUERY = gql`
  query GetCategoryByHandle(
    $partnerId: String!
    $storeChannel: String!
    $handle: String!
  ) {
    getCategoryByHandle(
      partnerId: $partnerId
      storeChannel: $storeChannel
      handle: $handle
    ) {
      id
      title
      image
      icon
      parentId
      level
      handle
      child {
        id
        title
        image
        icon
        parentId
        level
        handle
      }
    }
  }
`;

export const GET_CATEGORY_BY_ID_QUERY = gql`
  query GetCategoryById(
    $partnerId: String!
    $storeChannel: String!
    $categoryId: String!
  ) {
    getCategoryById(
      partnerId: $partnerId
      storeChannel: $storeChannel
      categoryId: $categoryId
    ) {
      id
      title
      image
      icon
      parentId
      level
      handle
      child {
        id
        title
        image
        icon
        parentId
        level
        handle
      }
    }
  }
`;

export const GET_BRANDS_QUERY = `
query GetBrands($partnerId: String!, $storeChannel: String!, $enable: Boolean) {
	getBrands(partnerId: $partnerId, storeChannel: $storeChannel, enable: $enable) {
		id
		name
		image
		imageIcon
	}
}
`;
export const GET_BRANDS_BY_CATEGORY_QUERY = `
query GetBrandsByCategory($partnerId: String!, $storeChannel: String!, $categoryId: String!) {
	getBrandsByCategory(partnerId: $partnerId, storeChannel: $storeChannel, categoryId: $categoryId) {
		id
		name
		image
		imageIcon
	}
}
`;
export const GET_BRAND_DETAIL_QUERY = `
  query GetBrandDetail($partnerId: String!, $brandId: String!, $storeChannel: String!) {
    getDetail(partnerId: $partnerId, brandId: $brandId, storeChannel: $storeChannel) {
      id
      name
      image
      imageIcon
    }
  }
`;

export const GET_PRODUCT_OPTION = gql`
  query GetProductOption(
    $partnerId: String!
    $storeChannel: String!
    $productId: String!
  ) {
    getProductOption(
      partnerId: $partnerId
      storeChannel: $storeChannel
      productId: $productId
    ) {
      id
      name
      subType
      productFeatureDTOS {
        productOptionGroupItemDTOS {
          id
          productId
          productName
          price
        }
      }
    }
  }
`;

export const GET_POLICY = gql`
  query GetPolicy($groupId: String!) {
    getPolicy(groupId: $groupId) {
      value
    }
  }
`;
export const GET_STORES = gql`
  query GetStores($partnerId: String!, $type: String!) {
    getStores(partnerId: $partnerId, type: $type) {
      id
      createdStamp
      name
      type
      enable
      partyId
      warehouses
      warehouseIdDefault
      enableOrderAbleFuture
      enableOrderNegativeQuantity
      storeEcommerceName
      shippingCompanies
      shippingCompanyIdPrimary
      customerIdPrimary
      paymentMethodIdPrimary
      enableCustomProductPrice
      enablePaymentPartial
      paymentPartialPercent
      productStoreLink
    }
  }
`;
export const GET_DETAIL_STORES = gql`
  query GetDetailStores($partnerId: String!, $storeId: String!) {
    getDetailStores(partnerId: $partnerId, storeId: $storeId) {
      id
      createdStamp
      name
      type
      enable
      partyId
      warehouses
      warehouseIdDefault
      enableOrderAbleFuture
      enableOrderNegativeQuantity
      storeEcommerceName
      shippingCompanies
      shippingCompanyIdPrimary
      customerIdPrimary
      paymentMethodIdPrimary
      enableCustomProductPrice
      enablePaymentPartial
      paymentPartialPercent
      productStoreLink
    }
  }
`;

export const GET_PRODUCT_IMAGE = gql`
  query GetProductImage($partnerId: String!, $productId: String!) {
    getProductImage(partnerId: $partnerId, productId: $productId)
  }
`;
export const GET_PRODUCT = gql`
  query GetProducts(
    $partnerId: String!
    $storeChannel: String!
    $keyword: String
    $category: String
    $currentPage: Int
    $maxResult: Int
  ) {
    getProducts(
      partnerId: $partnerId
      storeChannel: $storeChannel
      keyword: $keyword
      category: $category
      currentPage: $currentPage
      maxResult: $maxResult
    ) {
      total
      currentPage
      maxResult
      totalPage
      data {
        id
        title
        subType
        description
        sku
        shortDescription
        weight
        width
        depth
        height
        vat
        qualify
        parentId
        handle
        price
        priceType
        salePolicy
        priceTypeName
        priceVaries
        available
        tags
        options
        optionsRelationship
        compareAtPrice
        featuredImage
        images
        categories {
          id
          title
          image
          icon
          parentId
          level
          handle
          description
        }
        groups {
          id
          name
          policy
          image
        }
        unitDTO {
          id
          name
        }
      }
    }
  }
`;
export const GET_UNITS = gql`
  query GetUnits($partnerId: String!) {
    getUnits(partnerId: $partnerId) {
      id
      name
    }
  }
`;
