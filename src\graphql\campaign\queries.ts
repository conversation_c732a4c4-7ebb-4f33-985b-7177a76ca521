import { gql } from "graphql-tag";

export const GET_CAMPAIGN_ACTION_ACTIVE_NOW = gql`
  query GetCampaignActionActiveNow(
    $partyId: String!
    $customerId: String
    $campaignActionType: String
    $productStoreId: String
  ) {
    getCampaignActionActiveNow(
      checkCampaignActiveRequest: {
        partyId: $partyId
        customerId: $customerId
        campaignActionType: $campaignActionType
        productStoreId: $productStoreId
      }
    ) {
      campaignId
      campaignName
      statusCampaign
      campaignDescription
      fromDate
      toDate
      campaignActionId
      campaignActionName
      type
      campaignActionDescription
      priorityLevel
    }
  }
`;
export const GET_VOUCHERS = gql`
  query SearchVoucher(
    $partyId: String
    $storeId: String
    $campaignId: String
    $campaignActionId: String
    $campaignActionType: String
    $customerId: String
    $excludeExpired: Boolean
    $pageNumber: Int
    $pageSize: Int
  ) {
    searchVoucher(
      searchVoucherRequest: {
        partyId: $partyId
        storeId: $storeId
        campaignId: $campaignId
        campaignActionId: $campaignActionId
        campaignActionType: $campaignActionType
        customerId: $customerId
        excludeExpired: $excludeExpired
        pageNumber: $pageNumber
        pageSize: $pageSize
      }
    ) {
      total
      totalPages
      totalElements
      last
      first
      number
      numberOfElements
      size
      empty
      content {
        campaignActionId
        campaignId
        partyId
        voucherCode
        voucherType
        status
        discountAmount
        discountPercent
        usageLimitPerVoucher
        maximumDiscount
        numberOfTimeUsed
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        isBirthday
        customerId
      }
    }
  }
`;

export const CHECK_VALID_VOUCHER = gql`
  query CheckValidVoucher(
    $partyId: String
    $productStoreId: String
    $customerId: String
    $voucherCode: String!
  ) {
    checkValidVoucher(
      checkValidVoucherRequest: {
        customerId: $customerId
        voucherCode: $voucherCode
        productStoreId: $productStoreId
      }
      partyId: $partyId
    ) {
      campaignActionId
      campaignId
      partyId
      voucherCode
      voucherType
      status
      discountAmount
      discountPercent
      usageLimitPerVoucher
      maximumDiscount
      numberOfTimeUsed
      id
      createdStamp
      updatedStamp
      updatedBy
      createdBy
    }
  }
`;
export const GET_CAMPAIGN_ACTIVE_NOW = gql`
  query GetCampaignActiveNow(
    $partyId: String!
    $productStoreId: String
    $campaignActionType: String
    $customerId: String
  ) {
    getCampaignActiveNow(
      checkCampaignActiveRequest: {
        partyId: $partyId
        customerId: $customerId
        campaignActionType: $campaignActionType
        productStoreId: $productStoreId
      }
    ) {
      campaign {
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        useForAll
        description
        partyId
        name
        type
        domain
        fromDate
        toDate
        status
      }
      campaignActions {
        name
        description
        partyId
        type
        viewTemplateId
        urlTemplate
        shortCode
        uriPattern
        parameterPattern
        status
        extendDaysForHeadReview
        priorityLevel
        positionConnectionType
        baseCommissionPercent
        targetType
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
      }
    }
  }
`;
export const GET_PROMOTION_PRODUCT_PRICE = gql`
  query GetPromotionProductPrice(
    $partyId: String!
    $productStoreId: String!
    $productId: String!
    $productPrice: BigDecimal!
  ) {
    getPromotionProductPrice(
      partyId: $partyId
      productStoreId: $productStoreId
      productId: $productId
      productPrice: $productPrice
    ) {
      campaignId
      campaignName
      statusCampaign
      campaignDescription
      fromDate
      toDate
      campaignPrivateUse
      useWithOtherCampaignIds
      campaignActionId
      campaignActionName
      type
      campaignActionDescription
      priorityLevel
      campaignActionPrivateUse
      useWithOtherCampaignActionIds
    }
  }
`;
export const GET_VOUCHER_AVAILABLE_FOR_CUSTOMER = gql`
  query GetVoucherAvailableForCustomer(
    $partyId: String
    $storeId: String
    $campaignId: String
    $campaignActionId: String
    $customerId: String
    $excludeExpired: Boolean
    $isPageAble: Boolean
  ) {
    getVoucherAvailableForCustomer(
      searchVoucherRequest: {
        partyId: $partyId
        storeId: $storeId
        campaignId: $campaignId
        campaignActionId: $campaignActionId
        customerId: $customerId
        excludeExpired: $excludeExpired
        isPageAble: $isPageAble
      }
    ) {
      total
      totalPages
      totalElements
      last
      first
      number
      numberOfElements
      size
      empty
      content {
        campaignActionId
        campaignId
        partyId
        voucherCode
        voucherType
        status
        discountAmount
        discountPercent
        usageLimitPerVoucher
        maximumDiscount
        numberOfTimeUsed
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        isBirthday
        customerId
      }
    }
  }
`;
export const SUGGEST_VOUCHER = gql`
  query SuggestVoucher(
    $partyId: String
    $customerId: String
    $excludeExpired: Boolean
  ) {
    suggestVoucher(
      searchVoucherRequest: {
        partyId: $partyId
        customerId: $customerId
        excludeExpired: $excludeExpired
      }
    ) {
      total
      totalPages
      totalElements
      last
      first
      number
      numberOfElements
      size
      empty
      content {
        campaignActionId
        campaignId
        partyId
        voucherCode
        voucherType
        status
        discountAmount
        discountPercent
        usageLimitPerVoucher
        maximumDiscount
        numberOfTimeUsed
        id
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        isBirthday
        customerId
        scope
        affiliateId
        maximumSpend
        minimumSpend
      }
    }
  }
`;
// query GetCampaignActionById {
//   getCampaignActionById(id: "20.1273.888") {
//       name
//       description
//       partyId
//       type
//       viewTemplateId
//       urlTemplate
//       shortCode
//       uriPattern
//       parameterPattern
//       status
//       extendDaysForHeadReview
//       priorityLevel
//       positionConnectionType
//       baseCommissionPercent
//       targetType
//       id
//       createdStamp
//       updatedStamp
//       updatedBy
//       createdBy
//   }
// }

export const GET_CAMPAIGN_ACTION_BY_ID = gql`
  query GetCampaignActionById($id: String!) {
    getCampaignActionById(id: $id) {
      name
      description
      partyId
      type
      viewTemplateId
      urlTemplate
      shortCode
      uriPattern
      parameterPattern
      status
      extendDaysForHeadReview
      priorityLevel
      positionConnectionType
      baseCommissionPercent
      targetType
      id
      createdStamp
      updatedStamp
      updatedBy
      createdBy
    }
  }
`;
export const SEARCH_PRODUCT_GIFT_PROMOTION_RESPONSE = gql`
  query SearchProductGiftPromotionResponse(
    $partyId: String!
    $storeId: String
    $campaignActionId: String
  ) {
    searchProductGiftPromotionResponse(
      parameterSearchProductGift: {
        partyId: $partyId
        storeId: $storeId
        campaignActionId: $campaignActionId
      }
    ) {
      total
      totalPages
      totalElements
      last
      first
      number
      numberOfElements
      size
      empty
      content {
        id
        partyId
        campaignId
        campaignActionId
        productId
        quantityLimit
        campaignActionName
        startDate
        endDate
        newCustomer
        createdStamp
        updatedStamp
        updatedBy
        createdBy
        giftPromotions {
          fromQuantity
          toProductId
          toQuantity
          productName
          sku
          featureImage
        }
      }
    }
  }
`;
