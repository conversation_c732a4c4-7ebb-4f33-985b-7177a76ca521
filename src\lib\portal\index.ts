import { Service } from "../serviceSDK";
export class PortalService extends Service {
  constructor(endpoint: string, orgId: string, storeId: string) {
    super(endpoint, orgId, storeId);
  }
  // GET
  async getDynamicForm(dataRequest: any) {
    const endpoint = `/dynamic-collection/public/v2/formApi/F.diaries`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        dataRequest
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  // POST
  async updateDynamicForm(dataRequest: any) {
    const endpoint = `/dynamic-collection/public/v2/formApi/F.diaries`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        dataRequest
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  // TAG-order
  //Create- tag
  async createTag(title: string, createBy: string, partnerId?: string) {
    const endpoint = `/tag-api/public/tag-core/tag/create?title=${title}&createBy=${createBy}&partnerId=${this.orgId}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  // search tag
  async searchTag(
    partnerId?: string,
    title?: string,
    id?: string,
    search?: string
  ) {
    const endpoint = `/tag-api/public/tag-core/tag/get?partnerId=${this.orgId}&title=${title}&id=${id}&search=${search}`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  //
  redirectLink(sku?: string, id?: string) {
    const url = `${
      this.endpoint
    }/facility/manager/session/inventory-item-by-product.xhtml?${
      sku ? `sku=${sku}` : `id=${id}`
    }&orgId=${this.orgId}`;
    return url;
  }
  imageProduct(
    parentId: string,
    parentType: string,
    width?: number,
    height?: number
  ) {
    const url = `${
      this.endpoint
    }/image-gateway/public/image/${parentType}/${parentId}${
      width ? `?w=${width}` : ``
    }${`${height ? `&h=${height}` : ``}`} `;
    return url;
  }
  imagesProduct(
    parentId: string,
    parentType: string,
    width?: number,
    height?: number
  ) {
    const url = `${this.endpoint}/image-gateway/public/images/${parentType}/${parentId}?w=${width}&h=${height}`;
    return url;
  }
  async completeOrder(orderId: string, byUser: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/complete-fulfillment/${byUser}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithToken(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async pushMessage(message: any) {
    const endpoint = `/dynamic-collection/public/v2/webhook/push_message`;
    const method: "POST" = "POST";
    console.log("endpoint", endpoint);
    const data = message;
    try {
      const response = await this.restApiCallWithNoToken(
        endpoint,
        method,
        message
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
  async confirmExport(orderId: string, updateBy: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/confirm-export/${updateBy}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async confirmPackage(orderId: string, packageBoxId: string, byUser: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/confirm-package/${packageBoxId}/${byUser}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async handlePackage(orderId: string, byUser: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/handle-package/${byUser}`;
    const method: "PUT" = "PUT";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async packageBoxes() {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/packageBoxes`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async shipmentParameter(orderId: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/shipment-parameters`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async connectShipment(orderId: string, byUser: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/connect-shipment/${byUser}`;
    const method: "POST" = "POST";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async ffmStage(orderId: string) {
    const endpoint = `/fulfillment-api/public/fulfillment/${this.orgId}/${orderId}/fulfillment-stage`;
    const method: "GET" = "GET";
    try {
      const response = await this.restApiCallWithNoHeader(endpoint, method);
      return response;
    } catch (error) {
      throw error;
    }
  }
  async completeCancelFFMOrder(
    orderId: string,
    note: string,
    reason: string
  ) {
    const endpoint = `/fulfillment-api/public/fulfillment/FOX/${orderId}/completeCancelOrder`;
    const method: "PUT" = "PUT";
    const reqData = {
      note: note,
      reason: reason,
    };
    try {
      const response = await this.restApiCallWithNoHeader(
        endpoint,
        method,
        reqData
      );
      return response;
    } catch (error) {
      throw error;
    }
  }
}
